<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#F5F5F5">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:id="@+id/title_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="📊 录屏性能分析报告"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#333333" />

        <Button
            android:id="@+id/close_button"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="关闭"
            android:textSize="14sp"
            android:background="#E0E0E0"
            android:textColor="#666666"
            android:minWidth="60dp" />

    </LinearLayout>

    <!-- 摘要信息卡片 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:background="#FFFFFF"
        android:elevation="4dp"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="📋 性能摘要"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="12dp" />

        <TextView
            android:id="@+id/summary_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="#666666"
            android:lineSpacingExtra="4dp"
            android:typeface="monospace" />

    </LinearLayout>

    <!-- 详细报告区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginBottom="16dp"
        android:background="#FFFFFF"
        android:elevation="4dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- 详细报告标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="16dp"
                android:background="#FAFAFA">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="📈 详细分析报告"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333" />

                <Button
                    android:id="@+id/save_button"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="保存报告"
                    android:textSize="14sp"
                    android:background="#4CAF50"
                    android:textColor="#FFFFFF"
                    android:minWidth="80dp" />

            </LinearLayout>

            <!-- 分隔线 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E0E0E0" />

            <!-- 可滚动的详细内容 -->
            <ScrollView
                android:id="@+id/scroll_view"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:padding="16dp"
                android:scrollbars="vertical">

                <TextView
                    android:id="@+id/detail_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textColor="#444444"
                    android:lineSpacingExtra="2dp"
                    android:typeface="monospace"
                    android:textIsSelectable="true" />

            </ScrollView>

        </LinearLayout>

    </LinearLayout>

    <!-- 底部操作栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="#FFFFFF"
        android:padding="12dp"
        android:elevation="2dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="💡 提示: 可以长按文本进行复制，或点击保存按钮将报告保存到文件"
            android:textSize="12sp"
            android:textColor="#888888"
            android:gravity="center" />

    </LinearLayout>

</LinearLayout>
