1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.xiaopeng.xpautotest.trace" >
4
5    <uses-sdk android:minSdkVersion="28" />
6
7    <application>
7-->E:\WorkSpace\code\xpAutoTest\trace\src\main\AndroidManifest.xml:4:5-11:19
8        <service
8-->E:\WorkSpace\code\xpAutoTest\trace\src\main\AndroidManifest.xml:5:9-10:19
9            android:name="com.xiaopeng.xpautotest.trace.service.CanDataCollectService"
9-->E:\WorkSpace\code\xpAutoTest\trace\src\main\AndroidManifest.xml:6:13-87
10            android:enabled="true"
10-->E:\WorkSpace\code\xpAutoTest\trace\src\main\AndroidManifest.xml:7:13-35
11            android:exported="false"
11-->E:\WorkSpace\code\xpAutoTest\trace\src\main\AndroidManifest.xml:8:13-37
12            android:foregroundServiceType="connectedDevice" >
12-->E:\WorkSpace\code\xpAutoTest\trace\src\main\AndroidManifest.xml:9:13-60
13        </service>
14    </application>
15
16</manifest>
