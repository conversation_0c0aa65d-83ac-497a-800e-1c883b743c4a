package com.xiaopeng.xpautotest.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Debug;
import android.os.Process;
import android.util.Log;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 性能监控器
 * 负责监控应用的CPU使用率和内存占用，支持周期性数据收集和记录
 */
public class PerformanceMonitor {
    private static final String TAG = "PerformanceMonitor";
    private static final int DEFAULT_MONITOR_INTERVAL = 2000; // 2秒
    
    private Context context;
    private ScheduledExecutorService executorService;
    private boolean isMonitoring = false;
    private int monitorInterval = DEFAULT_MONITOR_INTERVAL;
    
    // CPU监控相关
    private long lastCpuTime = 0;
    private long lastAppCpuTime = 0;
    private int pid;
    
    // 性能数据回调
    public interface PerformanceCallback {
        void onPerformanceData(PerformanceData data);
    }
    
    private PerformanceCallback callback;
    
    /**
     * 性能数据实体类
     */
    public static class PerformanceData {
        public long timestamp;
        public double cpuUsage;        // CPU使用率 (%)
        public long memoryPss;         // PSS内存 (KB)
        public long memoryPrivateDirty; // 私有脏内存 (KB)
        public long memorySharedDirty;  // 共享脏内存 (KB)
        
        @Override
        public String toString() {
            return String.format("PerformanceData{timestamp=%d, cpuUsage=%.2f%%, memoryPss=%dKB, memoryPrivateDirty=%dKB, memorySharedDirty=%dKB}",
                timestamp, cpuUsage, memoryPss, memoryPrivateDirty, memorySharedDirty);
        }
    }
    
    public PerformanceMonitor(Context context) {
        this.context = context;
        this.pid = Process.myPid();
        this.executorService = Executors.newSingleThreadScheduledExecutor();
    }
    
    /**
     * 设置监控间隔
     * @param intervalMs 监控间隔（毫秒）
     */
    public void setMonitorInterval(int intervalMs) {
        this.monitorInterval = intervalMs;
    }
    
    /**
     * 设置性能数据回调
     */
    public void setPerformanceCallback(PerformanceCallback callback) {
        this.callback = callback;
    }
    
    /**
     * 开始性能监控
     */
    public void startMonitoring() {
        if (isMonitoring) {
            Log.w(TAG, "Performance monitoring is already running");
            return;
        }
        
        isMonitoring = true;
        
        // 初始化CPU监控基准值
        initializeCpuMonitoring();
        
        // 启动周期性监控任务
        executorService.scheduleAtFixedRate(this::collectPerformanceData, 
            0, monitorInterval, TimeUnit.MILLISECONDS);
        
        Log.i(TAG, "Performance monitoring started with interval: " + monitorInterval + "ms");
    }
    
    /**
     * 停止性能监控
     */
    public void stopMonitoring() {
        if (!isMonitoring) {
            return;
        }
        
        isMonitoring = false;
        
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(3, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        Log.i(TAG, "Performance monitoring stopped");
    }
    
    /**
     * 收集性能数据
     */
    private void collectPerformanceData() {
        try {
            PerformanceData data = new PerformanceData();
            data.timestamp = System.currentTimeMillis();
            
            // 获取CPU使用率
            data.cpuUsage = getCpuUsage();
            
            // 获取内存信息
            getMemoryInfo(data);
            
            // 记录到日志
            Log.d(TAG, "Performance: " + data.toString());
            
            // 回调通知
            if (callback != null) {
                callback.onPerformanceData(data);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error collecting performance data", e);
        }
    }
    
    /**
     * 初始化CPU监控
     */
    private void initializeCpuMonitoring() {
        try {
            lastCpuTime = getTotalCpuTime();
            lastAppCpuTime = getAppCpuTime();
        } catch (Exception e) {
            Log.e(TAG, "Error initializing CPU monitoring", e);
        }
    }
    
    /**
     * 获取CPU使用率
     */
    private double getCpuUsage() {
        try {
            long currentCpuTime = getTotalCpuTime();
            long currentAppCpuTime = getAppCpuTime();
            
            if (lastCpuTime == 0 || lastAppCpuTime == 0) {
                lastCpuTime = currentCpuTime;
                lastAppCpuTime = currentAppCpuTime;
                return 0.0;
            }
            
            long totalCpuDiff = currentCpuTime - lastCpuTime;
            long appCpuDiff = currentAppCpuTime - lastAppCpuTime;
            
            double cpuUsage = 0.0;
            if (totalCpuDiff > 0) {
                cpuUsage = (double) appCpuDiff / totalCpuDiff * 100.0;
            }
            
            lastCpuTime = currentCpuTime;
            lastAppCpuTime = currentAppCpuTime;
            
            return Math.max(0.0, Math.min(100.0, cpuUsage));
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting CPU usage", e);
            return 0.0;
        }
    }
    
    /**
     * 获取系统总CPU时间
     */
    private long getTotalCpuTime() throws IOException {
        try (BufferedReader reader = new BufferedReader(new FileReader("/proc/stat"))) {
            String line = reader.readLine();
            if (line != null && line.startsWith("cpu ")) {
                String[] tokens = line.split("\\s+");
                long totalTime = 0;
                // 累加所有CPU时间（user, nice, system, idle, iowait, irq, softirq）
                for (int i = 1; i < Math.min(tokens.length, 8); i++) {
                    totalTime += Long.parseLong(tokens[i]);
                }
                return totalTime;
            }
        }
        return 0;
    }
    
    /**
     * 获取应用CPU时间
     */
    private long getAppCpuTime() throws IOException {
        try (BufferedReader reader = new BufferedReader(new FileReader("/proc/" + pid + "/stat"))) {
            String line = reader.readLine();
            if (line != null) {
                String[] tokens = line.split("\\s+");
                if (tokens.length >= 15) {
                    // utime (14) + stime (15) = 应用CPU时间
                    long utime = Long.parseLong(tokens[13]);
                    long stime = Long.parseLong(tokens[14]);
                    return utime + stime;
                }
            }
        }
        return 0;
    }
    
    /**
     * 获取内存信息
     */
    private void getMemoryInfo(PerformanceData data) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager != null) {
                Debug.MemoryInfo[] memoryInfos = activityManager.getProcessMemoryInfo(new int[]{pid});
                if (memoryInfos.length > 0) {
                    Debug.MemoryInfo memoryInfo = memoryInfos[0];
                    data.memoryPss = memoryInfo.getTotalPss();
                    data.memoryPrivateDirty = memoryInfo.getTotalPrivateDirty();
                    data.memorySharedDirty = memoryInfo.getTotalSharedDirty();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting memory info", e);
            data.memoryPss = 0;
            data.memoryPrivateDirty = 0;
            data.memorySharedDirty = 0;
        }
    }
    
    /**
     * 获取当前性能快照
     */
    public PerformanceData getCurrentPerformanceSnapshot() {
        PerformanceData data = new PerformanceData();
        data.timestamp = System.currentTimeMillis();
        data.cpuUsage = getCpuUsage();
        getMemoryInfo(data);
        return data;
    }
    
    /**
     * 检查是否正在监控
     */
    public boolean isMonitoring() {
        return isMonitoring;
    }
}
