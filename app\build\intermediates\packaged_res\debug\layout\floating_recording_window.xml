<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/recording_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/floating_window_background"
    android:orientation="horizontal"
    android:padding="8dp"
    android:gravity="center_vertical"
    android:elevation="8dp"
    android:alpha="0.9">

    <!-- 录制图标 -->
    <ImageView
        android:id="@+id/record_icon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/ic_record_red"
        android:layout_marginEnd="6dp" />

    <!-- 录制时间 -->
    <TextView
        android:id="@+id/recording_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="00:00"
        android:textColor="@android:color/white"
        android:textSize="11sp"
        android:textStyle="bold"
        android:minWidth="40dp"
        android:gravity="center" />

</LinearLayout>
