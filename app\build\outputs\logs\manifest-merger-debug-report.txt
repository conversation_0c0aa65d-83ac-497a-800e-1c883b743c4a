-- Merging decision tree log ---
manifest
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:2:1-132:12
INJECTED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:2:1-132:12
INJECTED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:2:1-132:12
INJECTED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:2:1-132:12
MERGED from [:executor] E:\WorkSpace\code\xpAutoTest\executor\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-16:12
MERGED from [:community] E:\WorkSpace\code\xpAutoTest\community\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.xiaopeng.lib:lib_xpui:5.6.3] C:\Users\<USER>\.gradle\caches\transforms-3\fc66c71e6d2f7749ba39454a10f3998a\transformed\jetified-lib_xpui-5.6.3\AndroidManifest.xml:2:1-11:12
MERGED from [com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:2:1-16:12
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:2:1-43:12
MERGED from [com.xiaopeng.lib.framework:datalogmodule:2.0.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\400a1860a3e30982162ca242df06b777\transformed\jetified-datalogmodule-2.0.1.5\AndroidManifest.xml:2:1-14:12
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:2:1-28:12
MERGED from [com.xiaopeng.lib.framework:systemdelegate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\910f5b3b08182507d3c00f8bd5e381b6\transformed\jetified-systemdelegate-1.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.xiaopeng.lib:lib_utils:*******] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-*******\AndroidManifest.xml:2:1-15:12
MERGED from [com.xiaopeng.lib:lib_config:1.2.7.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc3dea4d2e074318ef29331ced026db0\transformed\jetified-lib_config-1.2.7.3\AndroidManifest.xml:2:1-16:12
MERGED from [com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:2:1-18:12
MERGED from [io.reactivex.rxjava2:rxandroid:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5543da45aa52af8f8284fe05592a3a17\transformed\jetified-rxandroid-2.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.xiaopeng.lib.framework:moduleinterface:2.1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac5370f517c2a982e8d9832015c132a1\transformed\jetified-moduleinterface-2.1.3.1\AndroidManifest.xml:2:1-9:12
MERGED from [org.greenrobot:eventbus:3.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d6c048e40f4b7dfb745278512cb35263\transformed\jetified-eventbus-3.3.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.aliyun.dpa:oss-android-sdk:2.9.12] C:\Users\<USER>\.gradle\caches\transforms-3\a2d75a02b6768523d24439f69215ac84\transformed\jetified-oss-android-sdk-2.9.12\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\93d9df77809f9ff1d7d7a500adecfe6e\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\33378cebc978c7dec42cc9fcf942d450\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e7380ec0bcad29d0bee0b20633953fd0\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9fb248392f055438974c3c6bf62bfc1\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:2:1-22:12
MERGED from [com.quickbirdstudios:opencv:4.5.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4149e22b19e056571b20659435ea2fe0\transformed\jetified-opencv-4.5.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android:flexbox:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7999496f81fa22f5b0945afbf5d9ea7f\transformed\jetified-flexbox-1.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [io.github.scwang90:refresh-layout-kernel:2.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\918ab13013ce5c0831ecd3ff7e2b362c\transformed\jetified-refresh-layout-kernel-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [com.tencent.tav:libpag:4.3.57] C:\Users\<USER>\.gradle\caches\transforms-3\18e2cfcfeb2ef4ecac79845f5e449e07\transformed\jetified-libpag-4.3.57\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\7c21b5b8cd66dde52b4a74a51a557481\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.xiaopeng.lib:lib_vuicommons:2.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a87af520236493ce2bc2ab6d3eab6215\transformed\jetified-lib_vuicommons-2.1.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.xiaopeng.lib:lib_widget:0.0.24-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\c811836c57f8a4666a4e9b550a5427ba\transformed\jetified-lib_widget-0.0.24-SNAPSHOT\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b836ec0f1af425fb839c94a04cf90c8\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2277750fe564e11450f53a70bab0febc\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b3a94a1ed0edc10174feead4b0800bc\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6ddbbf8ec23d1afd151a6e3f3c758669\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:17:1-31:12
MERGED from [com.xiaopeng.kms:KmsSdk:V1.0.7] C:\Users\<USER>\.gradle\caches\transforms-3\7d12817846b7438a077f11f4caa1ff0f\transformed\jetified-KmsSdk-V1.0.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d552060688b1fa053ca89046e6aee1e\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.tencent.mars:mars-xlog:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\92b5e1928283ea59a0f5a26c4fd54fd1\transformed\jetified-mars-xlog-1.2.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.aliyun.ams:alicloud-android-httpdns:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\f9e3a2fed88dffb2db55b7df2cc3e344\transformed\jetified-alicloud-android-httpdns-2.4.2\AndroidManifest.xml:2:1-12:12
MERGED from [com.xiaopeng.lib:lib_mqtt:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e101570c4ea8165190ad91032c6955f8\transformed\jetified-lib_mqtt-1.1.1\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d520f2b8a7bc33935fbb1b019d97f31f\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7404c4d00efe20cb16788fa2d4b984d\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7dc9f8ca26c028bd39efeac6396d389\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e56e5982943bb6a57107b864468c0b67\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a446aa9097b9882348e33229f7a1dfbc\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\214b9e19cf95e01d9bcde2f7eab5d49b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0b0768b223b1334fed2a8f97d5f5fdf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5066e82580250c42fb531b5c382b3297\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fe561cc2fe42dd30f91e98638456750\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\1705eb812d1b89421291bd89d65c9f22\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ec773d7d9bfadd8be1d2b8d3fb414922\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\39ff34341b72f9913047a0589d5a1d20\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\f4bd846bc9de862d59329cce7493d735\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\068205c9cd5f6a67a05b16ac091b3988\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\f332d19440955d023cdb93dc61d8ce19\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\40d51a84e7e02935f763bb61231cd6de\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a955ee317c496b9da8e3afa6a354040\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\aaff42ba767b74121bfd049653c77c88\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f29ed29267c66d27bc991e297822027a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abb1aa348a257b131622f74fbed22c98\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b7e911aaf50ebd2a65ce09da064eeb5\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c76a3321453fd7933d86227159fc9bfa\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\602257aa1c86bf997860c4fa96041f08\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d30ca949149d5a093c654dd7db4b506d\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6758eb8874c37f00364c3059e36ed338\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8105abc6563ded885e4d28a82501afd\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0cb2dcd89a52d4d1105f01e466a5445c\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bcee09691a1b0315b85e831daea820a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6887382acfa24af54ec4c3b375fd55a6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\96708237b71f707bdf77728248e24381\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\74f14e90253635cc5b18f101510384f9\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ccedf46b69b47bf03623a15024d05da7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4022a6671ca956cd9626ef14198eb06a\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5e0d50be7011248ca3e433ab073aad9\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\980ada3e3dd7ba1db641e490a84adf7b\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.aliyun.ams:alicloud-android-logger:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfddad1d8b9352edb5f6c66c75ea6303\transformed\jetified-alicloud-android-logger-1.2.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.aliyun.ams:alicloud-android-ipdetector:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4deb1bcdae6a12643853e3b84fb6791\transformed\jetified-alicloud-android-ipdetector-1.1.0\AndroidManifest.xml:2:1-11:12
	package
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:5:5-38
		INJECTED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml
	android:sharedUserId
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:4:5-46
	android:versionName
		INJECTED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:2:11-69
	android:sharedUserMaxSdkVersion
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:6:5-41
uses-permission#android.permission.INSTALL_PACKAGES
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:7:5-75
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:7:22-72
uses-permission#android.permission.DELETE_PACKAGES
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:8:5-74
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:8:22-71
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:10:5-76
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:13:5-76
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:13:5-76
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:16:5-76
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:16:5-76
MERGED from [com.xiaopeng.lib:lib_utils:*******] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-*******\AndroidManifest.xml:7:5-76
MERGED from [com.xiaopeng.lib:lib_utils:*******] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-*******\AndroidManifest.xml:7:5-76
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:10:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:11:5-76
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:14:5-76
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:14:5-76
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:11:22-73
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:12:5-79
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:15:5-79
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:15:5-79
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:12:22-76
uses-permission#android.permission.INTERNET
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:13:5-67
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:16:5-67
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:16:5-67
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:11:5-67
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:11:5-67
MERGED from [com.aliyun.dpa:oss-android-sdk:2.9.12] C:\Users\<USER>\.gradle\caches\transforms-3\a2d75a02b6768523d24439f69215ac84\transformed\jetified-oss-android-sdk-2.9.12\AndroidManifest.xml:11:5-67
MERGED from [com.aliyun.dpa:oss-android-sdk:2.9.12] C:\Users\<USER>\.gradle\caches\transforms-3\a2d75a02b6768523d24439f69215ac84\transformed\jetified-oss-android-sdk-2.9.12\AndroidManifest.xml:11:5-67
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6ddbbf8ec23d1afd151a6e3f3c758669\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:26:5-67
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6ddbbf8ec23d1afd151a6e3f3c758669\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:26:5-67
MERGED from [com.aliyun.ams:alicloud-android-httpdns:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\f9e3a2fed88dffb2db55b7df2cc3e344\transformed\jetified-alicloud-android-httpdns-2.4.2\AndroidManifest.xml:10:5-67
MERGED from [com.aliyun.ams:alicloud-android-httpdns:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\f9e3a2fed88dffb2db55b7df2cc3e344\transformed\jetified-alicloud-android-httpdns-2.4.2\AndroidManifest.xml:10:5-67
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:13:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:14:5-79
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:17:5-79
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:17:5-79
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:15:5-79
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:15:5-79
MERGED from [com.aliyun.ams:alicloud-android-httpdns:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\f9e3a2fed88dffb2db55b7df2cc3e344\transformed\jetified-alicloud-android-httpdns-2.4.2\AndroidManifest.xml:9:5-79
MERGED from [com.aliyun.ams:alicloud-android-httpdns:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\f9e3a2fed88dffb2db55b7df2cc3e344\transformed\jetified-alicloud-android-httpdns-2.4.2\AndroidManifest.xml:9:5-79
MERGED from [com.xiaopeng.lib:lib_mqtt:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e101570c4ea8165190ad91032c6955f8\transformed\jetified-lib_mqtt-1.1.1\AndroidManifest.xml:9:5-79
MERGED from [com.xiaopeng.lib:lib_mqtt:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e101570c4ea8165190ad91032c6955f8\transformed\jetified-lib_mqtt-1.1.1\AndroidManifest.xml:9:5-79
MERGED from [com.aliyun.ams:alicloud-android-ipdetector:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4deb1bcdae6a12643853e3b84fb6791\transformed\jetified-alicloud-android-ipdetector-1.1.0\AndroidManifest.xml:9:5-79
MERGED from [com.aliyun.ams:alicloud-android-ipdetector:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4deb1bcdae6a12643853e3b84fb6791\transformed\jetified-alicloud-android-ipdetector-1.1.0\AndroidManifest.xml:9:5-79
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:14:22-76
uses-permission#android.permission.READ_PHONE_STATE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:15:5-75
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:18:5-75
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:18:5-75
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:18:5-75
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:18:5-75
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:15:22-72
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:16:5-81
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:19:5-81
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:19:5-81
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:12:5-81
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:12:5-81
MERGED from [com.aliyun.dpa:oss-android-sdk:2.9.12] C:\Users\<USER>\.gradle\caches\transforms-3\a2d75a02b6768523d24439f69215ac84\transformed\jetified-oss-android-sdk-2.9.12\AndroidManifest.xml:12:5-81
MERGED from [com.aliyun.dpa:oss-android-sdk:2.9.12] C:\Users\<USER>\.gradle\caches\transforms-3\a2d75a02b6768523d24439f69215ac84\transformed\jetified-oss-android-sdk-2.9.12\AndroidManifest.xml:12:5-81
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6ddbbf8ec23d1afd151a6e3f3c758669\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:28:5-81
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6ddbbf8ec23d1afd151a6e3f3c758669\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:28:5-81
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:16:22-78
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:17:5-81
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:20:5-81
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:20:5-81
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:17:22-78
uses-permission#android.permission.ACCESS_CACHE_FILESYSTEM
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:18:5-82
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:18:22-79
uses-permission#android.permission.REBOOT
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:19:5-65
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:19:22-62
uses-permission#android.permission.WRITE_MEDIA_STORAGE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:20:5-78
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:20:22-75
uses-permission#android.permission.CONNECTIVITY_INTERNAL
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:21:5-80
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:21:22-77
uses-permission#android.permission.MOUNT_UNMOUNT_FILESYSTEMS
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:22:5-84
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:22:5-84
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:22:5-84
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6ddbbf8ec23d1afd151a6e3f3c758669\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:29:5-84
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6ddbbf8ec23d1afd151a6e3f3c758669\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:29:5-84
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:22:22-81
uses-permission#android.permission.DOWNLOAD_WITHOUT_NOTIFICATION
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:23:5-88
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:23:22-85
uses-permission#android.permission.ACCESS_DOWNLOAD_MANAGER
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:24:5-82
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:24:22-79
uses-permission#android.permission.WAKE_LOCK
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:25:5-68
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:21:5-68
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:21:5-68
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:25:22-65
uses-permission#android.permission.DEVICE_POWER
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:26:5-71
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:26:22-68
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:27:5-80
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:23:5-80
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:23:5-80
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:13:5-80
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:13:5-80
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6ddbbf8ec23d1afd151a6e3f3c758669\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:27:5-80
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6ddbbf8ec23d1afd151a6e3f3c758669\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:27:5-80
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:27:22-77
uses-permission#android.permission.READ_LOGS
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:28:5-68
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:28:22-65
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:29:5-78
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:29:22-75
uses-permission#android.permission.RECOVERY
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:30:5-67
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:30:22-64
uses-permission#android.permission.INTERACT_ACROSS_USERS
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:31:5-80
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:28:5-80
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:28:5-80
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:31:22-77
uses-permission#android.permission.KILL_BACKGROUND_PROCESSES
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:32:5-84
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:32:22-81
uses-permission#android.permission.SUPER_APPLICATION_RUNNING
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:33:5-84
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:33:22-81
uses-permission#android.car.permission.CAR_VENDOR_EXTENSION
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:34:5-83
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:34:22-80
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:35:5-85
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:35:22-82
uses-permission#android.permission.MEDIA_PROJECTION
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:36:5-75
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:36:22-72
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:37:5-77
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:37:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:38:5-94
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:38:22-91
uses-permission#com.xiaopeng.permission.OTA_SERVICE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:39:5-75
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:39:22-72
uses-permission#com.xiaopeng.permission.CAR_SERVICE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:40:5-75
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:40:22-72
uses-permission#com.xiaopeng.permission.ACTIVITY
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:41:5-72
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:41:22-69
uses-permission#com.xiaopeng.permission.SERVICE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:42:5-71
MERGED from [com.xiaopeng.lib.framework:datalogmodule:2.0.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\400a1860a3e30982162ca242df06b777\transformed\jetified-datalogmodule-2.0.1.5\AndroidManifest.xml:10:5-71
MERGED from [com.xiaopeng.lib.framework:datalogmodule:2.0.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\400a1860a3e30982162ca242df06b777\transformed\jetified-datalogmodule-2.0.1.5\AndroidManifest.xml:10:5-71
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:42:22-68
uses-permission#com.xiaopeng.permission.BROADCAST
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:43:5-73
MERGED from [com.xiaopeng.lib.framework:datalogmodule:2.0.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\400a1860a3e30982162ca242df06b777\transformed\jetified-datalogmodule-2.0.1.5\AndroidManifest.xml:11:5-73
MERGED from [com.xiaopeng.lib.framework:datalogmodule:2.0.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\400a1860a3e30982162ca242df06b777\transformed\jetified-datalogmodule-2.0.1.5\AndroidManifest.xml:11:5-73
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:43:22-70
application
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:44:5-130:19
MERGED from [:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-14:19
MERGED from [:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-14:19
MERGED from [com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:11:5-14:19
MERGED from [com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:11:5-14:19
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:31:5-41:19
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:31:5-41:19
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:23:5-26:19
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:23:5-26:19
MERGED from [com.xiaopeng.lib:lib_utils:*******] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-*******\AndroidManifest.xml:10:5-13:19
MERGED from [com.xiaopeng.lib:lib_utils:*******] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-*******\AndroidManifest.xml:10:5-13:19
MERGED from [com.xiaopeng.lib:lib_config:1.2.7.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc3dea4d2e074318ef29331ced026db0\transformed\jetified-lib_config-1.2.7.3\AndroidManifest.xml:11:5-14:19
MERGED from [com.xiaopeng.lib:lib_config:1.2.7.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc3dea4d2e074318ef29331ced026db0\transformed\jetified-lib_config-1.2.7.3\AndroidManifest.xml:11:5-14:19
MERGED from [com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:10:5-16:19
MERGED from [com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\93d9df77809f9ff1d7d7a500adecfe6e\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\93d9df77809f9ff1d7d7a500adecfe6e\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\33378cebc978c7dec42cc9fcf942d450\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\33378cebc978c7dec42cc9fcf942d450\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:13:5-20:19
MERGED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:13:5-20:19
MERGED from [com.tencent.tav:libpag:4.3.57] C:\Users\<USER>\.gradle\caches\transforms-3\18e2cfcfeb2ef4ecac79845f5e449e07\transformed\jetified-libpag-4.3.57\AndroidManifest.xml:7:5-20
MERGED from [com.tencent.tav:libpag:4.3.57] C:\Users\<USER>\.gradle\caches\transforms-3\18e2cfcfeb2ef4ecac79845f5e449e07\transformed\jetified-libpag-4.3.57\AndroidManifest.xml:7:5-20
MERGED from [com.xiaopeng.lib:lib_mqtt:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e101570c4ea8165190ad91032c6955f8\transformed\jetified-lib_mqtt-1.1.1\AndroidManifest.xml:11:5-13:38
MERGED from [com.xiaopeng.lib:lib_mqtt:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e101570c4ea8165190ad91032c6955f8\transformed\jetified-lib_mqtt-1.1.1\AndroidManifest.xml:11:5-13:38
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bcee09691a1b0315b85e831daea820a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bcee09691a1b0315b85e831daea820a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6887382acfa24af54ec4c3b375fd55a6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6887382acfa24af54ec4c3b375fd55a6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:47:9-41
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:51:9-35
	android:label
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:50:9-41
		REJECTED from [com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:12:9-41
		REJECTED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:25:9-41
		REJECTED from [com.xiaopeng.lib:lib_utils:*******] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-*******\AndroidManifest.xml:11:9-41
		REJECTED from [com.xiaopeng.lib:lib_config:1.2.7.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc3dea4d2e074318ef29331ced026db0\transformed\jetified-lib_config-1.2.7.3\AndroidManifest.xml:12:9-41
		REJECTED from [com.xiaopeng.lib:lib_mqtt:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e101570c4ea8165190ad91032c6955f8\transformed\jetified-lib_mqtt-1.1.1\AndroidManifest.xml:12:9-41
	android:configChanges
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:46:9-67
	android:allowBackup
		ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:32:9-36
		REJECTED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:24:9-36
	android:icon
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:49:9-43
	android:theme
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:52:9-49
	android:networkSecurityConfig
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:48:9-69
	tools:replace
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:53:9-38
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:45:9-51
meta-data#com.xiaopeng.lib.lib_feature_modules
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:54:9-56:109
	android:value
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:56:13-106
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:55:13-64
activity#com.xiaopeng.xpautotest.ui.MainActivity
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:57:9-68:20
	android:launchMode
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:61:13-48
	android:exported
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:60:13-36
	android:configChanges
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:59:13-204
	android:theme
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:62:13-53
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:58:13-67
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:64:13-67:29
action#android.intent.action.MAIN
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:65:17-69
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:65:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:66:17-77
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:66:27-74
activity#com.xiaopeng.xpautotest.ui.PerformanceReportActivity
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:71:9-75:56
	android:exported
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:74:13-37
	android:configChanges
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:73:13-204
	android:theme
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:75:13-53
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:72:13-80
activity#com.xiaopeng.xpautotest.debug.ScreenRecordTestActivity
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:78:9-82:56
	android:exported
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:81:13-37
	android:configChanges
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:80:13-204
	android:theme
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:82:13-53
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:79:13-82
service#com.xiaopeng.xpautotest.service.TestExecutionService
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:84:9-88:62
	android:enabled
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:86:13-35
	android:exported
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:87:13-37
	android:foregroundServiceType
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:88:13-60
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:85:13-80
service#com.xiaopeng.xpautotest.service.DebuggingModeService
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:90:9-94:62
	android:enabled
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:92:13-35
	android:exported
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:93:13-37
	android:foregroundServiceType
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:94:13-60
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:91:13-80
service#com.xiaopeng.xpautotest.accessibility.AutoTestAccessibilityService
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:96:9-107:19
	android:enabled
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:99:13-35
	android:exported
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:100:13-36
	android:permission
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:98:13-79
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:97:13-71
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:101:13-103:29
action#android.accessibilityservice.AccessibilityService
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:102:17-92
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:102:25-89
meta-data#android.accessibilityservice
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:104:13-106:58
	android:resource
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:106:17-55
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:105:17-60
receiver#com.xiaopeng.xpautotest.receiver.StartBroadcastReceiver
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:109:9-115:20
	android:enabled
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:110:13-35
	android:exported
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:111:13-36
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:109:19-89
intent-filter#action:name:com.xiaoppeng.xpautotest.OPEN_APP_ACTION
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:112:13-114:29
action#com.xiaoppeng.xpautotest.OPEN_APP_ACTION
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:113:17-83
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:113:25-80
service#com.xiaopeng.xpautotest.service.OSSUploadService
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:116:9-122:19
	android:exported
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:118:13-36
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:117:13-76
intent-filter#action:name:com.xiaopeng.xpautotest.action.START_OSS_UPLOAD_SERVICE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:119:13-121:29
action#com.xiaopeng.xpautotest.action.START_OSS_UPLOAD_SERVICE
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:120:17-98
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:120:25-95
service#com.xiaopeng.xpautotest.service.ScreenRecordService
ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:125:9-129:63
	android:enabled
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:127:13-35
	android:exported
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:128:13-37
	android:foregroundServiceType
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:129:13-60
	android:name
		ADDED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:126:13-79
uses-sdk
INJECTED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml
INJECTED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml
MERGED from [:executor] E:\WorkSpace\code\xpAutoTest\executor\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:executor] E:\WorkSpace\code\xpAutoTest\executor\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:community] E:\WorkSpace\code\xpAutoTest\community\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:community] E:\WorkSpace\code\xpAutoTest\community\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [com.xiaopeng.lib:lib_xpui:5.6.3] C:\Users\<USER>\.gradle\caches\transforms-3\fc66c71e6d2f7749ba39454a10f3998a\transformed\jetified-lib_xpui-5.6.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.xiaopeng.lib:lib_xpui:5.6.3] C:\Users\<USER>\.gradle\caches\transforms-3\fc66c71e6d2f7749ba39454a10f3998a\transformed\jetified-lib_xpui-5.6.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:5:5-7:41
MERGED from [com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:5:5-7:41
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:9:5-11:41
MERGED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:9:5-11:41
MERGED from [com.xiaopeng.lib.framework:datalogmodule:2.0.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\400a1860a3e30982162ca242df06b777\transformed\jetified-datalogmodule-2.0.1.5\AndroidManifest.xml:5:5-7:41
MERGED from [com.xiaopeng.lib.framework:datalogmodule:2.0.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\400a1860a3e30982162ca242df06b777\transformed\jetified-datalogmodule-2.0.1.5\AndroidManifest.xml:5:5-7:41
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:6:5-9:58
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:6:5-9:58
MERGED from [com.xiaopeng.lib.framework:systemdelegate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\910f5b3b08182507d3c00f8bd5e381b6\transformed\jetified-systemdelegate-1.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.xiaopeng.lib.framework:systemdelegate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\910f5b3b08182507d3c00f8bd5e381b6\transformed\jetified-systemdelegate-1.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.xiaopeng.lib:lib_utils:*******] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-*******\AndroidManifest.xml:5:5-44
MERGED from [com.xiaopeng.lib:lib_utils:*******] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-*******\AndroidManifest.xml:5:5-44
MERGED from [com.xiaopeng.lib:lib_config:1.2.7.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc3dea4d2e074318ef29331ced026db0\transformed\jetified-lib_config-1.2.7.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.xiaopeng.lib:lib_config:1.2.7.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc3dea4d2e074318ef29331ced026db0\transformed\jetified-lib_config-1.2.7.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:6:5-8:41
MERGED from [com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:6:5-8:41
MERGED from [io.reactivex.rxjava2:rxandroid:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5543da45aa52af8f8284fe05592a3a17\transformed\jetified-rxandroid-2.1.0\AndroidManifest.xml:18:5-43
MERGED from [io.reactivex.rxjava2:rxandroid:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5543da45aa52af8f8284fe05592a3a17\transformed\jetified-rxandroid-2.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.xiaopeng.lib.framework:moduleinterface:2.1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac5370f517c2a982e8d9832015c132a1\transformed\jetified-moduleinterface-2.1.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.xiaopeng.lib.framework:moduleinterface:2.1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac5370f517c2a982e8d9832015c132a1\transformed\jetified-moduleinterface-2.1.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [org.greenrobot:eventbus:3.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d6c048e40f4b7dfb745278512cb35263\transformed\jetified-eventbus-3.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [org.greenrobot:eventbus:3.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d6c048e40f4b7dfb745278512cb35263\transformed\jetified-eventbus-3.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.aliyun.dpa:oss-android-sdk:2.9.12] C:\Users\<USER>\.gradle\caches\transforms-3\a2d75a02b6768523d24439f69215ac84\transformed\jetified-oss-android-sdk-2.9.12\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.dpa:oss-android-sdk:2.9.12] C:\Users\<USER>\.gradle\caches\transforms-3\a2d75a02b6768523d24439f69215ac84\transformed\jetified-oss-android-sdk-2.9.12\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\93d9df77809f9ff1d7d7a500adecfe6e\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\93d9df77809f9ff1d7d7a500adecfe6e\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\33378cebc978c7dec42cc9fcf942d450\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\33378cebc978c7dec42cc9fcf942d450\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e7380ec0bcad29d0bee0b20633953fd0\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e7380ec0bcad29d0bee0b20633953fd0\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9fb248392f055438974c3c6bf62bfc1\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9fb248392f055438974c3c6bf62bfc1\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.quickbirdstudios:opencv:4.5.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4149e22b19e056571b20659435ea2fe0\transformed\jetified-opencv-4.5.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.quickbirdstudios:opencv:4.5.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4149e22b19e056571b20659435ea2fe0\transformed\jetified-opencv-4.5.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android:flexbox:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7999496f81fa22f5b0945afbf5d9ea7f\transformed\jetified-flexbox-1.0.0\AndroidManifest.xml:23:5-25:41
MERGED from [com.google.android:flexbox:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7999496f81fa22f5b0945afbf5d9ea7f\transformed\jetified-flexbox-1.0.0\AndroidManifest.xml:23:5-25:41
MERGED from [io.github.scwang90:refresh-layout-kernel:2.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\918ab13013ce5c0831ecd3ff7e2b362c\transformed\jetified-refresh-layout-kernel-2.0.6\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-layout-kernel:2.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\918ab13013ce5c0831ecd3ff7e2b362c\transformed\jetified-refresh-layout-kernel-2.0.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.tencent.tav:libpag:4.3.57] C:\Users\<USER>\.gradle\caches\transforms-3\18e2cfcfeb2ef4ecac79845f5e449e07\transformed\jetified-libpag-4.3.57\AndroidManifest.xml:5:5-44
MERGED from [com.tencent.tav:libpag:4.3.57] C:\Users\<USER>\.gradle\caches\transforms-3\18e2cfcfeb2ef4ecac79845f5e449e07\transformed\jetified-libpag-4.3.57\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\7c21b5b8cd66dde52b4a74a51a557481\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\7c21b5b8cd66dde52b4a74a51a557481\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.xiaopeng.lib:lib_vuicommons:2.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a87af520236493ce2bc2ab6d3eab6215\transformed\jetified-lib_vuicommons-2.1.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.xiaopeng.lib:lib_vuicommons:2.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a87af520236493ce2bc2ab6d3eab6215\transformed\jetified-lib_vuicommons-2.1.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.xiaopeng.lib:lib_widget:0.0.24-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\c811836c57f8a4666a4e9b550a5427ba\transformed\jetified-lib_widget-0.0.24-SNAPSHOT\AndroidManifest.xml:7:5-9:41
MERGED from [com.xiaopeng.lib:lib_widget:0.0.24-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\c811836c57f8a4666a4e9b550a5427ba\transformed\jetified-lib_widget-0.0.24-SNAPSHOT\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b836ec0f1af425fb839c94a04cf90c8\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b836ec0f1af425fb839c94a04cf90c8\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2277750fe564e11450f53a70bab0febc\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2277750fe564e11450f53a70bab0febc\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b3a94a1ed0edc10174feead4b0800bc\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b3a94a1ed0edc10174feead4b0800bc\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6ddbbf8ec23d1afd151a6e3f3c758669\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:22:5-24:41
MERGED from [com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6ddbbf8ec23d1afd151a6e3f3c758669\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:22:5-24:41
MERGED from [com.xiaopeng.kms:KmsSdk:V1.0.7] C:\Users\<USER>\.gradle\caches\transforms-3\7d12817846b7438a077f11f4caa1ff0f\transformed\jetified-KmsSdk-V1.0.7\AndroidManifest.xml:5:5-44
MERGED from [com.xiaopeng.kms:KmsSdk:V1.0.7] C:\Users\<USER>\.gradle\caches\transforms-3\7d12817846b7438a077f11f4caa1ff0f\transformed\jetified-KmsSdk-V1.0.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d552060688b1fa053ca89046e6aee1e\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d552060688b1fa053ca89046e6aee1e\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.tencent.mars:mars-xlog:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\92b5e1928283ea59a0f5a26c4fd54fd1\transformed\jetified-mars-xlog-1.2.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.tencent.mars:mars-xlog:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\92b5e1928283ea59a0f5a26c4fd54fd1\transformed\jetified-mars-xlog-1.2.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.aliyun.ams:alicloud-android-httpdns:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\f9e3a2fed88dffb2db55b7df2cc3e344\transformed\jetified-alicloud-android-httpdns-2.4.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.aliyun.ams:alicloud-android-httpdns:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\f9e3a2fed88dffb2db55b7df2cc3e344\transformed\jetified-alicloud-android-httpdns-2.4.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.xiaopeng.lib:lib_mqtt:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e101570c4ea8165190ad91032c6955f8\transformed\jetified-lib_mqtt-1.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.xiaopeng.lib:lib_mqtt:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e101570c4ea8165190ad91032c6955f8\transformed\jetified-lib_mqtt-1.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d520f2b8a7bc33935fbb1b019d97f31f\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d520f2b8a7bc33935fbb1b019d97f31f\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7404c4d00efe20cb16788fa2d4b984d\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7404c4d00efe20cb16788fa2d4b984d\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7dc9f8ca26c028bd39efeac6396d389\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7dc9f8ca26c028bd39efeac6396d389\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e56e5982943bb6a57107b864468c0b67\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e56e5982943bb6a57107b864468c0b67\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a446aa9097b9882348e33229f7a1dfbc\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a446aa9097b9882348e33229f7a1dfbc\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\214b9e19cf95e01d9bcde2f7eab5d49b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\214b9e19cf95e01d9bcde2f7eab5d49b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0b0768b223b1334fed2a8f97d5f5fdf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0b0768b223b1334fed2a8f97d5f5fdf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5066e82580250c42fb531b5c382b3297\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5066e82580250c42fb531b5c382b3297\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fe561cc2fe42dd30f91e98638456750\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fe561cc2fe42dd30f91e98638456750\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\1705eb812d1b89421291bd89d65c9f22\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\1705eb812d1b89421291bd89d65c9f22\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ec773d7d9bfadd8be1d2b8d3fb414922\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ec773d7d9bfadd8be1d2b8d3fb414922\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\39ff34341b72f9913047a0589d5a1d20\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\39ff34341b72f9913047a0589d5a1d20\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\f4bd846bc9de862d59329cce7493d735\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\f4bd846bc9de862d59329cce7493d735\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\068205c9cd5f6a67a05b16ac091b3988\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\068205c9cd5f6a67a05b16ac091b3988\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\f332d19440955d023cdb93dc61d8ce19\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\f332d19440955d023cdb93dc61d8ce19\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\40d51a84e7e02935f763bb61231cd6de\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\40d51a84e7e02935f763bb61231cd6de\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a955ee317c496b9da8e3afa6a354040\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a955ee317c496b9da8e3afa6a354040\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\aaff42ba767b74121bfd049653c77c88\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\aaff42ba767b74121bfd049653c77c88\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f29ed29267c66d27bc991e297822027a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f29ed29267c66d27bc991e297822027a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abb1aa348a257b131622f74fbed22c98\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abb1aa348a257b131622f74fbed22c98\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b7e911aaf50ebd2a65ce09da064eeb5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b7e911aaf50ebd2a65ce09da064eeb5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c76a3321453fd7933d86227159fc9bfa\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c76a3321453fd7933d86227159fc9bfa\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\602257aa1c86bf997860c4fa96041f08\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\602257aa1c86bf997860c4fa96041f08\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d30ca949149d5a093c654dd7db4b506d\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d30ca949149d5a093c654dd7db4b506d\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6758eb8874c37f00364c3059e36ed338\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6758eb8874c37f00364c3059e36ed338\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8105abc6563ded885e4d28a82501afd\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8105abc6563ded885e4d28a82501afd\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0cb2dcd89a52d4d1105f01e466a5445c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0cb2dcd89a52d4d1105f01e466a5445c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bcee09691a1b0315b85e831daea820a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bcee09691a1b0315b85e831daea820a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6887382acfa24af54ec4c3b375fd55a6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6887382acfa24af54ec4c3b375fd55a6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\96708237b71f707bdf77728248e24381\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\96708237b71f707bdf77728248e24381\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\74f14e90253635cc5b18f101510384f9\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\74f14e90253635cc5b18f101510384f9\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ccedf46b69b47bf03623a15024d05da7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ccedf46b69b47bf03623a15024d05da7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4022a6671ca956cd9626ef14198eb06a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4022a6671ca956cd9626ef14198eb06a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5e0d50be7011248ca3e433ab073aad9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5e0d50be7011248ca3e433ab073aad9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\980ada3e3dd7ba1db641e490a84adf7b\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\980ada3e3dd7ba1db641e490a84adf7b\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.aliyun.ams:alicloud-android-logger:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfddad1d8b9352edb5f6c66c75ea6303\transformed\jetified-alicloud-android-logger-1.2.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.ams:alicloud-android-logger:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfddad1d8b9352edb5f6c66c75ea6303\transformed\jetified-alicloud-android-logger-1.2.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.aliyun.ams:alicloud-android-ipdetector:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4deb1bcdae6a12643853e3b84fb6791\transformed\jetified-alicloud-android-ipdetector-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.aliyun.ams:alicloud-android-ipdetector:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4deb1bcdae6a12643853e3b84fb6791\transformed\jetified-alicloud-android-ipdetector-1.1.0\AndroidManifest.xml:5:5-7:41
	tools:overrideLibrary
		ADDED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:9:9-55
	android:targetSdkVersion
		INJECTED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml
service#com.xiaopeng.xpautotest.trace.service.CanDataCollectService
ADDED from [:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-13:19
	android:enabled
		ADDED from [:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-35
	android:exported
		ADDED from [:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
	android:foregroundServiceType
		ADDED from [:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-60
	android:name
		ADDED from [:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-87
uses-permission#xiaopeng.permission.DATA_SERVICE
ADDED from [com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:9:5-72
	android:name
		ADDED from [com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:9:22-69
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:24:5-81
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:14:5-81
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:14:5-81
	android:name
		ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:24:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:25:5-79
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:17:5-79
MERGED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:17:5-79
	android:name
		ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:25:22-76
uses-permission#com.xiaopeng.permission.DATA_SERVICE
ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:26:5-76
MERGED from [com.xiaopeng.lib.framework:datalogmodule:2.0.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\400a1860a3e30982162ca242df06b777\transformed\jetified-datalogmodule-2.0.1.5\AndroidManifest.xml:12:5-76
MERGED from [com.xiaopeng.lib.framework:datalogmodule:2.0.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\400a1860a3e30982162ca242df06b777\transformed\jetified-datalogmodule-2.0.1.5\AndroidManifest.xml:12:5-76
	android:name
		ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:26:22-73
uses-permission#android.permission.INTERACT_ACROSS_USERS_FULL
ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:27:5-85
MERGED from [com.xiaopeng.lib.framework:datalogmodule:2.0.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\400a1860a3e30982162ca242df06b777\transformed\jetified-datalogmodule-2.0.1.5\AndroidManifest.xml:9:5-85
MERGED from [com.xiaopeng.lib.framework:datalogmodule:2.0.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\400a1860a3e30982162ca242df06b777\transformed\jetified-datalogmodule-2.0.1.5\AndroidManifest.xml:9:5-85
	android:name
		ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:27:22-82
uses-permission#android.permission.GET_TASKS
ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:29:5-68
	android:name
		ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:29:22-65
service#org.eclipse.paho.android.service.MqttService
ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:34:9-80
	android:name
		ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:34:18-77
provider#com.xiaopeng.lib.framework.netchannelmodule.common.ContextNetStatusProvider
ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:36:9-40:20
	android:authorities
		ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:38:13-70
	android:exported
		ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:39:13-37
	android:name
		ADDED from [com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:37:13-103
uses-permission#android.permission.CALL_PHONE
ADDED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:19:5-69
	android:name
		ADDED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:19:22-66
uses-permission#android.permission.READ_SMS
ADDED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:20:5-67
	android:name
		ADDED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:20:22-64
uses-permission#com.xiaopeng.permission.SYSTEM_DELEGATE
ADDED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:21:5-79
	android:name
		ADDED from [com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:21:22-76
uses-permission#android.permission.REORDER_TASKS
ADDED from [com.xiaopeng.lib:lib_utils:*******] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-*******\AndroidManifest.xml:8:5-72
	android:name
		ADDED from [com.xiaopeng.lib:lib_utils:*******] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-*******\AndroidManifest.xml:8:22-69
provider#com.xiaopeng.lib.apirouter.server.ApiPublisherProvider
ADDED from [com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:11:9-15:34
	tools:node
		ADDED from [com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:15:13-31
	android:authorities
		ADDED from [com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:13:13-65
	android:exported
		ADDED from [com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:14:13-36
	android:name
		ADDED from [com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:12:13-82
uses-permission#android.permission.XIAOPENG_APP
ADDED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:11:5-71
	android:name
		ADDED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:11:22-68
provider#com.xiaopeng.lib.feature.XpFeatureProvider
ADDED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:14:9-19:34
	tools:node
		ADDED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:19:13-31
	android:authorities
		ADDED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:16:13-69
	android:exported
		ADDED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:17:13-37
	android:initOrder
		ADDED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:18:13-37
	android:name
		ADDED from [com.xiaopeng.lib:lib_feature:5.8.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-5.8.0.1\AndroidManifest.xml:15:13-70
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6887382acfa24af54ec4c3b375fd55a6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6887382acfa24af54ec4c3b375fd55a6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.xiaopeng.xpautotest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.xiaopeng.xpautotest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
