1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.xiaopeng.xpautotest"
4    android:sharedUserId="android.uid.system"
5    android:sharedUserMaxSdkVersion="32"
6    android:versionCode="9999"
7    android:versionName="1.0.0-SNAPSHOT" >
8
9    <uses-sdk
10        android:minSdkVersion="28"
11        android:targetSdkVersion="30" />
12
13    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
13-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:7:5-75
13-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
14-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:8:5-74
14-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:8:22-71
15    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
15-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:10:5-76
15-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:10:22-73
16    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
16-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:11:5-76
16-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:11:22-73
17    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
17-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:12:5-79
17-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:12:22-76
18    <uses-permission android:name="android.permission.INTERNET" />
18-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:13:5-67
18-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:13:22-64
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:14:5-79
19-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:14:22-76
20    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
20-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:15:5-75
20-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:15:22-72
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:16:5-81
21-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:16:22-78
22    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
22-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:17:5-81
22-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:17:22-78
23    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
23-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:18:5-82
23-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:18:22-79
24    <uses-permission android:name="android.permission.REBOOT" />
24-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:19:5-65
24-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:19:22-62
25    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
25-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:20:5-78
25-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:20:22-75
26    <uses-permission android:name="android.permission.CONNECTIVITY_INTERNAL" />
26-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:21:5-80
26-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:21:22-77
27    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
27-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:22:5-84
27-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:22:22-81
28    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
28-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:23:5-88
28-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:23:22-85
29    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
29-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:24:5-82
29-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:24:22-79
30    <uses-permission android:name="android.permission.WAKE_LOCK" />
30-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:25:5-68
30-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:25:22-65
31    <uses-permission android:name="android.permission.DEVICE_POWER" />
31-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:26:5-71
31-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:26:22-68
32    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
32-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:27:5-80
32-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:27:22-77
33    <uses-permission android:name="android.permission.READ_LOGS" />
33-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:28:5-68
33-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:28:22-65
34    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
34-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:29:5-78
34-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:29:22-75
35    <uses-permission android:name="android.permission.RECOVERY" />
35-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:30:5-67
35-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:30:22-64
36    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
36-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:31:5-80
36-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:31:22-77
37    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
37-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:32:5-84
37-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:32:22-81
38    <uses-permission android:name="android.permission.SUPER_APPLICATION_RUNNING" />
38-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:33:5-84
38-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:33:22-81
39    <uses-permission android:name="android.car.permission.CAR_VENDOR_EXTENSION" />
39-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:34:5-83
39-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:34:22-80
40    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
40-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:35:5-85
40-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:35:22-82
41    <uses-permission android:name="android.permission.MEDIA_PROJECTION" />
41-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:36:5-75
41-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:36:22-72
42    <uses-permission android:name="android.permission.RECORD_AUDIO" />
42-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:37:5-71
42-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:37:22-68
43    <uses-permission android:name="android.permission.CAMERA" />
43-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:38:5-65
43-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:38:22-62
44    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
44-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:39:5-77
44-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:39:22-74
45    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
45-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:40:5-94
45-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:40:22-91
46    <uses-permission android:name="com.xiaopeng.permission.OTA_SERVICE" />
46-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:41:5-75
46-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:41:22-72
47    <uses-permission android:name="com.xiaopeng.permission.CAR_SERVICE" />
47-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:42:5-75
47-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:42:22-72
48    <uses-permission android:name="com.xiaopeng.permission.ACTIVITY" />
48-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:43:5-72
48-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:43:22-69
49    <uses-permission android:name="com.xiaopeng.permission.SERVICE" />
49-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:44:5-71
49-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:44:22-68
50    <uses-permission android:name="com.xiaopeng.permission.BROADCAST" />
50-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:45:5-73
50-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:45:22-70
51    <uses-permission android:name="xiaopeng.permission.DATA_SERVICE" />
51-->[com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:9:5-72
51-->[com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:9:22-69
52    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
52-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:24:5-81
52-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:24:22-78
53    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
53-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:25:5-79
53-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:25:22-76
54    <uses-permission android:name="com.xiaopeng.permission.DATA_SERVICE" />
54-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:26:5-76
54-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:26:22-73
55    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
55-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:27:5-85
55-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:27:22-82
56    <uses-permission android:name="android.permission.GET_TASKS" />
56-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:29:5-68
56-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:29:22-65
57    <uses-permission android:name="android.permission.CALL_PHONE" />
57-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:19:5-69
57-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:19:22-66
58    <uses-permission android:name="android.permission.READ_SMS" />
58-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:20:5-67
58-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:20:22-64
59    <uses-permission android:name="com.xiaopeng.permission.SYSTEM_DELEGATE" />
59-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:21:5-79
59-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:21:22-76
60    <uses-permission android:name="android.permission.REORDER_TASKS" />
60-->[com.xiaopeng.lib:lib_utils:1.7.5.9] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-1.7.5.9\AndroidManifest.xml:8:5-72
60-->[com.xiaopeng.lib:lib_utils:1.7.5.9] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-1.7.5.9\AndroidManifest.xml:8:22-69
61    <uses-permission android:name="android.permission.XIAOPENG_APP" />
61-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:11:5-71
61-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:11:22-68
62
63    <permission
63-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
64        android:name="com.xiaopeng.xpautotest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
64-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
65        android:protectionLevel="signature" />
65-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
66
67    <uses-permission android:name="com.xiaopeng.xpautotest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
67-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
67-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
68
69    <application
69-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:46:5-123:19
70        android:name="com.xiaopeng.xpautotest.App"
70-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:47:9-51
71        android:allowBackup="false"
71-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:32:9-36
72        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
72-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
73        android:configChanges="keyboard|keyboardHidden|navigation"
73-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:48:9-67
74        android:debuggable="true"
75        android:extractNativeLibs="true"
75-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:49:9-41
76        android:icon="@mipmap/ic_launcher"
76-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:51:9-43
77        android:label="@string/app_name"
77-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:52:9-41
78        android:networkSecurityConfig="@xml/network_security_config"
78-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:50:9-69
79        android:supportsRtl="true"
79-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:53:9-35
80        android:testOnly="true"
81        android:theme="@style/autotest_AppTheme" >
81-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:54:9-49
82        <meta-data
82-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:56:9-58:109
83            android:name="com.xiaopeng.lib.lib_feature_modules"
83-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:57:13-64
84            android:value="com.xiaopeng.carcontrol,com.xiaopeng.caraccount,com.xiaopeng.carspeechservice" />
84-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:58:13-106
85
86        <activity
86-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:59:9-70:20
87            android:name="com.xiaopeng.xpautotest.ui.MainActivity"
87-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:60:13-67
88            android:configChanges="navigation|colorMode|mnc|mcc|fontScale|keyboard|keyboardHidden|screenSize|smallestScreenSize|density|locale|uiMode|orientation|layoutDirection|screenLayout|touchscreen"
88-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:61:13-204
89            android:exported="true"
89-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:62:13-36
90            android:launchMode="singleInstance"
90-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:63:13-48
91            android:theme="@style/autotest_AppTheme" >
91-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:64:13-53
92            <intent-filter>
92-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:66:13-69:29
93                <action android:name="android.intent.action.MAIN" />
93-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:67:17-69
93-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:67:25-66
94
95                <category android:name="android.intent.category.LAUNCHER" />
95-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:68:17-77
95-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:68:27-74
96            </intent-filter>
97        </activity>
98        <activity
98-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:72:9-76:56
99            android:name="com.xiaopeng.xpautotest.ui.ScreenRecordActivity"
99-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:73:13-75
100            android:configChanges="navigation|colorMode|mnc|mcc|fontScale|keyboard|keyboardHidden|screenSize|smallestScreenSize|density|locale|uiMode|orientation|layoutDirection|screenLayout|touchscreen"
100-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:74:13-204
101            android:exported="false"
101-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:75:13-37
102            android:theme="@style/autotest_AppTheme" />
102-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:76:13-53
103
104        <service
104-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:78:9-82:62
105            android:name="com.xiaopeng.xpautotest.service.TestExecutionService"
105-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:79:13-80
106            android:enabled="true"
106-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:80:13-35
107            android:exported="false"
107-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:81:13-37
108            android:foregroundServiceType="connectedDevice" />
108-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:82:13-60
109        <service
109-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:84:9-88:62
110            android:name="com.xiaopeng.xpautotest.service.DebuggingModeService"
110-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:85:13-80
111            android:enabled="true"
111-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:86:13-35
112            android:exported="false"
112-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:87:13-37
113            android:foregroundServiceType="connectedDevice" />
113-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:88:13-60
114        <service
114-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:90:9-94:63
115            android:name="com.xiaopeng.xpautotest.service.ScreenRecordService"
115-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:91:13-79
116            android:enabled="true"
116-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:92:13-35
117            android:exported="false"
117-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:93:13-37
118            android:foregroundServiceType="mediaProjection" />
118-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:94:13-60
119        <service
119-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:96:9-107:19
120            android:name="com.xiaopeng.xpautotest.accessibility.AutoTestAccessibilityService"
120-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:97:13-71
121            android:enabled="true"
121-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:99:13-35
122            android:exported="true"
122-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:100:13-36
123            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
123-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:98:13-79
124            <intent-filter>
124-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:101:13-103:29
125                <action android:name="android.accessibilityservice.AccessibilityService" />
125-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:102:17-92
125-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:102:25-89
126            </intent-filter>
127
128            <meta-data
128-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:104:13-106:58
129                android:name="android.accessibilityservice"
129-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:105:17-60
130                android:resource="@xml/service_config" />
130-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:106:17-55
131        </service>
132
133        <receiver
133-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:109:9-115:20
134            android:name="com.xiaopeng.xpautotest.receiver.StartBroadcastReceiver"
134-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:109:19-89
135            android:enabled="true"
135-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:110:13-35
136            android:exported="true" >
136-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:111:13-36
137            <intent-filter>
137-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:112:13-114:29
138                <action android:name="com.xiaoppeng.xpautotest.OPEN_APP_ACTION" />
138-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:113:17-83
138-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:113:25-80
139            </intent-filter>
140        </receiver>
141
142        <service
142-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:116:9-122:19
143            android:name="com.xiaopeng.xpautotest.service.OSSUploadService"
143-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:117:13-76
144            android:exported="true" >
144-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:118:13-36
145            <intent-filter>
145-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:119:13-121:29
146                <action android:name="com.xiaopeng.xpautotest.action.START_OSS_UPLOAD_SERVICE" />
146-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:120:17-98
146-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:120:25-95
147            </intent-filter>
148        </service>
149        <service
149-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-13:19
150            android:name="com.xiaopeng.xpautotest.trace.service.CanDataCollectService"
150-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-87
151            android:enabled="true"
151-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-35
152            android:exported="false"
152-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
153            android:foregroundServiceType="connectedDevice" >
153-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-60
154        </service>
155        <service android:name="org.eclipse.paho.android.service.MqttService" />
155-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:34:9-80
155-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:34:18-77
156
157        <provider
157-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:36:9-40:20
158            android:name="com.xiaopeng.lib.framework.netchannelmodule.common.ContextNetStatusProvider"
158-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:37:13-103
159            android:authorities="com.xiaopeng.xpautotest.netmodule.provider"
159-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:38:13-70
160            android:exported="false" >
160-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:39:13-37
161        </provider>
162        <provider
162-->[com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:11:9-15:34
163            android:name="com.xiaopeng.lib.apirouter.server.ApiPublisherProvider"
163-->[com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:12:13-82
164            android:authorities="com.xiaopeng.xpautotest.api.publisher"
164-->[com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:13:13-65
165            android:exported="true" />
165-->[com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:14:13-36
166        <provider
166-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:14:9-19:34
167            android:name="com.xiaopeng.lib.feature.XpFeatureProvider"
167-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:15:13-70
168            android:authorities="com.xiaopeng.xpautotest.XpFeatureProvider"
168-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:16:13-69
169            android:exported="false"
169-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:17:13-37
170            android:initOrder="1100" />
170-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:18:13-37
171        <provider
171-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
172            android:name="androidx.startup.InitializationProvider"
172-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
173            android:authorities="com.xiaopeng.xpautotest.androidx-startup"
173-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
174            android:exported="false" >
174-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
175            <meta-data
175-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
176                android:name="androidx.emoji2.text.EmojiCompatInitializer"
176-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
177                android:value="androidx.startup" />
177-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
178            <meta-data
178-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
179                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
179-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
180                android:value="androidx.startup" />
180-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
181            <meta-data
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
182                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
183                android:value="androidx.startup" />
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
184        </provider>
185
186        <receiver
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
187            android:name="androidx.profileinstaller.ProfileInstallReceiver"
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
188            android:directBootAware="false"
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
189            android:enabled="true"
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
190            android:exported="true"
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
191            android:permission="android.permission.DUMP" >
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
192            <intent-filter>
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
193                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
194            </intent-filter>
195            <intent-filter>
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
196                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
197            </intent-filter>
198            <intent-filter>
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
199                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
200            </intent-filter>
201            <intent-filter>
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
202                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
203            </intent-filter>
204        </receiver>
205    </application>
206
207</manifest>
