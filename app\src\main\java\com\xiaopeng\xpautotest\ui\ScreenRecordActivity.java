package com.xiaopeng.xpautotest.ui;

import android.Manifest;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.service.ScreenRecordService;
import com.xiaopeng.xpautotest.utils.PerformanceMonitor;
import com.xiaopeng.xui.app.XActivity;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 屏幕录制控制Activity
 * 提供录制控制界面、权限请求和性能数据显示
 */
public class ScreenRecordActivity extends XActivity {
    private static final String TAG = "ScreenRecordActivity";
    private static final int PERMISSION_REQUEST_CODE = 1001;
    
    // UI组件
    private Button btnStartRecord;
    private Button btnStopRecord;
    private Button btnClearLogs;
    private Button btnBack;
    private TextView tvStatus;
    private TextView tvPerformance;
    private TextView tvLogs;
    
    // 服务相关
    private ScreenRecordService recordService;
    private boolean isServiceBound = false;
    
    // UI更新
    private Handler uiHandler;
    private StringBuilder logBuilder;
    
    // 权限列表
    private static final String[] REQUIRED_PERMISSIONS = {
        Manifest.permission.WRITE_EXTERNAL_STORAGE,
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.RECORD_AUDIO // 如果需要录制音频
    };
    
    private ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            ScreenRecordService.ScreenRecordBinder binder = (ScreenRecordService.ScreenRecordBinder) service;
            recordService = binder.getService();
            isServiceBound = true;
            
            addLog("服务连接成功");
            updateUI();
            
            // 设置性能监控回调
            if (recordService.getPerformanceMonitor() != null) {
                recordService.getPerformanceMonitor().setPerformanceCallback(performanceData -> {
                    uiHandler.post(() -> updatePerformanceDisplay(performanceData));
                });
            }
        }
        
        @Override
        public void onServiceDisconnected(ComponentName name) {
            recordService = null;
            isServiceBound = false;
            addLog("服务连接断开");
            updateUI();
        }
    };
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_screen_record);
        
        initViews();
        initData();
        checkPermissions();
    }
    
    @Override
    protected void onStart() {
        super.onStart();
        bindService();
    }
    
    @Override
    protected void onStop() {
        super.onStop();
        unbindService();
    }
    
    /**
     * 初始化视图
     */
    private void initViews() {
        btnStartRecord = findViewById(R.id.btn_start_record);
        btnStopRecord = findViewById(R.id.btn_stop_record);
        btnClearLogs = findViewById(R.id.btn_clear_logs);
        btnBack = findViewById(R.id.btn_back);
        tvStatus = findViewById(R.id.tv_status);
        tvPerformance = findViewById(R.id.tv_performance);
        tvLogs = findViewById(R.id.tv_logs);
        
        // 设置点击事件
        btnStartRecord.setOnClickListener(v -> startRecording());
        btnStopRecord.setOnClickListener(v -> stopRecording());
        btnClearLogs.setOnClickListener(v -> clearLogs());
        btnBack.setOnClickListener(v -> finish());
    }
    
    /**
     * 初始化数据
     */
    private void initData() {
        uiHandler = new Handler(Looper.getMainLooper());
        logBuilder = new StringBuilder();
        
        addLog("屏幕录制界面初始化完成");
        updateUI();
    }
    
    /**
     * 检查权限
     */
    private void checkPermissions() {
        List<String> missingPermissions = new ArrayList<>();
        
        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                missingPermissions.add(permission);
            }
        }
        
        if (!missingPermissions.isEmpty()) {
            addLog("请求权限: " + missingPermissions.toString());
            ActivityCompat.requestPermissions(this, 
                missingPermissions.toArray(new String[0]), 
                PERMISSION_REQUEST_CODE);
        } else {
            addLog("所有权限已授予");
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            
            if (allGranted) {
                addLog("所有权限已授予");
                Toast.makeText(this, "权限授予成功", Toast.LENGTH_SHORT).show();
            } else {
                addLog("部分权限被拒绝");
                Toast.makeText(this, "需要所有权限才能正常录制", Toast.LENGTH_LONG).show();
            }
        }
    }
    
    /**
     * 绑定服务
     */
    private void bindService() {
        Intent intent = new Intent(this, ScreenRecordService.class);
        bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE);
    }
    
    /**
     * 解绑服务
     */
    private void unbindService() {
        if (isServiceBound) {
            unbindService(serviceConnection);
            isServiceBound = false;
        }
    }
    
    /**
     * 开始录制
     */
    private void startRecording() {
        if (recordService == null) {
            addLog("错误：服务未连接");
            Toast.makeText(this, "服务未连接", Toast.LENGTH_SHORT).show();
            return;
        }
        
        addLog("开始录制请求...");
        
        // 启动录制服务
        Intent intent = new Intent(this, ScreenRecordService.class);
        intent.setAction("START_RECORDING");
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent);
        } else {
            startService(intent);
        }
        
        // 等待一段时间后更新UI
        uiHandler.postDelayed(() -> {
            updateUI();
            if (recordService.isRecording()) {
                addLog("录制已开始");
                Toast.makeText(this, "录制已开始", Toast.LENGTH_SHORT).show();
            } else {
                addLog("录制启动失败");
                Toast.makeText(this, "录制启动失败", Toast.LENGTH_SHORT).show();
            }
        }, 2000);
    }
    
    /**
     * 停止录制
     */
    private void stopRecording() {
        if (recordService == null) {
            addLog("错误：服务未连接");
            return;
        }
        
        addLog("停止录制请求...");
        
        Intent intent = new Intent(this, ScreenRecordService.class);
        intent.setAction("STOP_RECORDING");
        startService(intent);
        
        uiHandler.postDelayed(() -> {
            updateUI();
            addLog("录制已停止");
            Toast.makeText(this, "录制已停止", Toast.LENGTH_SHORT).show();
        }, 1000);
    }
    
    /**
     * 更新UI状态
     */
    private void updateUI() {
        boolean isRecording = recordService != null && recordService.isRecording();
        
        btnStartRecord.setEnabled(!isRecording);
        btnStopRecord.setEnabled(isRecording);
        
        String status = isRecording ? "状态：正在录制" : "状态：未录制";
        tvStatus.setText(status);
    }
    
    /**
     * 更新性能显示
     */
    private void updatePerformanceDisplay(PerformanceMonitor.PerformanceData data) {
        String performanceText = String.format(Locale.getDefault(),
            "性能监控 [%s]\nCPU使用率: %.2f%%\nPSS内存: %d KB\n私有脏内存: %d KB\n共享脏内存: %d KB",
            new SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(new Date(data.timestamp)),
            data.cpuUsage,
            data.memoryPss,
            data.memoryPrivateDirty,
            data.memorySharedDirty
        );
        
        tvPerformance.setText(performanceText);
    }
    
    /**
     * 添加日志
     */
    private void addLog(String message) {
        String timestamp = new SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(new Date());
        String logMessage = String.format("[%s] %s\n", timestamp, message);
        
        logBuilder.append(logMessage);
        
        uiHandler.post(() -> {
            tvLogs.setText(logBuilder.toString());
            // 滚动到底部
            tvLogs.post(() -> {
                int scrollAmount = tvLogs.getLayout().getLineTop(tvLogs.getLineCount()) - tvLogs.getHeight();
                if (scrollAmount > 0) {
                    tvLogs.scrollTo(0, scrollAmount);
                } else {
                    tvLogs.scrollTo(0, 0);
                }
            });
        });
        
        Log.d(TAG, message);
    }
    
    /**
     * 清除日志
     */
    private void clearLogs() {
        logBuilder.setLength(0);
        tvLogs.setText("日志信息：\n");
        addLog("日志已清除");
    }
}
