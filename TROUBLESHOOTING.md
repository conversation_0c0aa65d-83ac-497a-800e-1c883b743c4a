# 屏幕录制功能故障排除指南

## 🚨 常见问题及解决方案

### 1. 权限被拒绝错误 (EACCES Permission denied)

**错误信息:**
```
java.io.FileNotFoundException: /data/local/tmp/minicap: open failed: EACCES (Permission denied)
```

**原因分析:**
- 应用无法写入 `/data/local/tmp/` 目录
- 缺少系统级权限
- SELinux策略限制

**解决方案:**

#### 方案1: 使用系统签名 (推荐)
```bash
# 确保应用使用系统签名
# 在AndroidManifest.xml中确认有:
android:sharedUserId="android.uid.system"
```

#### 方案2: 手动部署minicap文件
```bash
# 通过adb手动部署minicap
adb push minicap /data/local/tmp/
adb push minicap.so /data/local/tmp/
adb shell chmod 755 /data/local/tmp/minicap
adb shell chmod 644 /data/local/tmp/minicap.so
```

#### 方案3: 使用root权限
```bash
# 如果设备已root
adb shell su -c "mkdir -p /data/local/tmp"
adb shell su -c "chmod 777 /data/local/tmp"
```

#### 方案4: 使用模拟模式
应用已内置模拟模式，当minicap无法启动时会自动切换到模拟录制。

### 2. minicap进程启动失败

**错误信息:**
```
Minicap process failed to start
```

**解决方案:**

#### 检查minicap文件
```bash
# 验证文件存在且可执行
adb shell ls -la /data/local/tmp/minicap*
adb shell file /data/local/tmp/minicap
```

#### 检查架构兼容性
```bash
# 查看设备架构
adb shell getprop ro.product.cpu.abi
adb shell getprop ro.product.cpu.abilist

# 确保使用对应架构的minicap文件
```

#### 手动测试minicap
```bash
# 进入设备shell测试
adb shell
cd /data/local/tmp
LD_LIBRARY_PATH=. ./minicap -P 1920x1080@1920x1080/0 -S
```

### 3. 套接字连接失败

**错误信息:**
```
Failed to connect to minicap socket
```

**解决方案:**

#### 检查minicap进程
```bash
# 查看minicap是否运行
adb shell ps | grep minicap
adb shell netstat -an | grep minicap
```

#### 重启minicap
```bash
# 杀死现有进程并重启
adb shell pkill minicap
# 然后重新启动录制
```

### 4. 录制文件无法保存

**错误信息:**
```
Failed to create MediaStore entry
```

**解决方案:**

#### 检查存储权限
```xml
<!-- 确保AndroidManifest.xml中有以下权限 -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

#### 检查存储空间
```bash
# 查看可用存储空间
adb shell df -h
```

### 5. 性能监控数据异常

**错误信息:**
```
Error getting CPU usage / memory info
```

**解决方案:**

#### 检查proc文件系统权限
```bash
# 验证可以访问proc文件
adb shell cat /proc/stat
adb shell cat /proc/self/stat
```

#### 确认应用权限
```xml
<!-- 确保有读取系统信息的权限 -->
<uses-permission android:name="android.permission.READ_LOGS" />
```

## 🔧 调试工具和命令

### 1. 日志查看
```bash
# 查看应用日志
adb logcat -s ScreenRecordService MinicapManager PerformanceMonitor

# 查看系统日志
adb logcat | grep -i minicap

# 清除日志后重新测试
adb logcat -c
```

### 2. 文件系统检查
```bash
# 检查应用目录权限
adb shell ls -la /data/data/com.xiaopeng.xpautotest/

# 检查临时目录
adb shell ls -la /data/local/tmp/

# 检查SELinux状态
adb shell getenforce
adb shell ls -Z /data/local/tmp/
```

### 3. 进程监控
```bash
# 监控应用进程
adb shell top | grep xpautotest

# 查看内存使用
adb shell dumpsys meminfo com.xiaopeng.xpautotest

# 查看CPU使用
adb shell dumpsys cpuinfo | grep xpautotest
```

## 🎯 最佳实践

### 1. 部署前检查
- 确认设备架构并使用对应的minicap文件
- 验证应用具有系统签名
- 检查设备存储空间充足

### 2. 测试流程
1. 先手动部署minicap文件
2. 测试minicap能否独立运行
3. 再测试应用集成功能
4. 检查录制文件和性能数据

### 3. 生产环境
- 使用系统签名的APK
- 预装minicap文件到系统目录
- 配置适当的SELinux策略

## 📞 获取帮助

### 1. 收集诊断信息
```bash
# 生成完整的诊断报告
adb shell getprop > device_props.txt
adb logcat -d > logcat.txt
adb shell ps > processes.txt
adb shell ls -laR /data/local/tmp/ > tmp_files.txt
```

### 2. 常用检查命令
```bash
# 一键检查脚本
adb shell "echo '=== Device Info ===' && getprop ro.product.cpu.abi && echo '=== Minicap Files ===' && ls -la /data/local/tmp/minicap* && echo '=== Processes ===' && ps | grep minicap && echo '=== Storage ===' && df -h /data"
```

### 3. 重置环境
```bash
# 清理并重新开始
adb shell rm -f /data/local/tmp/minicap*
adb shell pkill minicap
# 然后重新部署和测试
```

## 🔄 版本兼容性

### Android版本支持
- ✅ Android 9.0+ (API 28+): 完全支持
- ⚠️ Android 8.0-8.1: 部分功能受限
- ❌ Android 7.x及以下: 不支持

### 设备架构支持
- ✅ arm64-v8a: 主要支持架构
- ✅ armeabi-v7a: 兼容支持
- ⚠️ x86/x86_64: 模拟器支持

---

**提示**: 如果所有方案都无法解决问题，应用会自动切换到模拟模式，仍可进行功能测试和性能监控。
