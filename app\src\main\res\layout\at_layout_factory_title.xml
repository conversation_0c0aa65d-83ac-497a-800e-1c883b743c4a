<?xml version="1.0" encoding="utf-8"?>
<com.xiaopeng.xui.widget.XConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/title_height">

    <com.xiaopeng.xui.widget.XImageView
        android:id="@+id/iv_title_logo"
        android:layout_width="@dimen/factory_title_image_width"
        android:layout_height="@dimen/factory_title_image_height"
        android:layout_marginStart="50dp"
        android:layout_marginTop="5dp"
        android:src="@mipmap/icon_xlogo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--    <com.xiaopeng.xui.widget.XImageView-->
    <!--        android:id="@+id/iv_title_maintain"-->
    <!--        android:layout_width="@dimen/factory_title_image_width"-->
    <!--        android:layout_height="@dimen/factory_title_image_height"-->
    <!--        android:layout_marginStart="20dp"-->
    <!--        android:src="@mipmap/icon_maintain"-->
    <!--        app:layout_constraintStart_toEndOf="@id/iv_title_logo"-->
    <!--        app:layout_constraintBaseline_toBaselineOf="@id/iv_title_logo" />-->

    <com.xiaopeng.xui.widget.XTextView
        android:id="@+id/tv_title_vatc_autotest"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginStart="10dp"
        android:textColor="@color/x_theme_text_01"
        android:textSize="@dimen/x_font_body_01_size"
        android:text="@string/autotest_title"
        app:layout_constraintStart_toEndOf="@id/iv_title_logo"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- time -->
    <!--    <com.xiaopeng.xui.widget.XTextView-->
    <!--        android:id="@+id/tv_title_time"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginTop="2dp"-->
    <!--        android:textColor="@color/x_theme_text_01"-->
    <!--        android:textSize="@dimen/x_font_body_03_size"-->
    <!--        app:layout_constraintStart_toStartOf="@id/iv_title_logo"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/iv_title_logo" />-->

    <!--<com.xiaopeng.xui.widget.XTextView
        android:id="@+id/tv_title_factory_mode"
        android:layout_width="210dp"
        android:layout_height="wrap_content"
        android:textColor="@color/x_theme_text_01"
        android:textSize="@dimen/x_font_title_03_size"
        android:fontFamily="@string/x_font_typeface_bold"
        android:text="@string/factory_title_factory_mode"
        app:layout_constraintStart_toStartOf="@id/tv_title_vatc_autotest"
        app:layout_constraintTop_toBottomOf="@id/tv_title_vatc_autotest" />-->

    <com.xiaopeng.xui.view.XView
        android:layout_width="2dp"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:background="@color/separator_line_bg"
        app:layout_constraintStart_toEndOf="@id/tv_title_vatc_autotest"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- vin -->
    <com.xiaopeng.xui.widget.XTextView
        android:id="@+id/tv_title_vin"
        android:layout_width="wrap_content"
        android:minWidth="300dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginStart="@dimen/factory_title_margin_separator"
        android:textColor="@color/x_theme_text_03"
        android:textSize="@dimen/x_font_body_03_size"
        android:text="@string/unknown"
        app:layout_constraintStart_toEndOf="@id/tv_title_vatc_autotest"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- CFC -->
    <com.xiaopeng.xui.widget.XTextView
        android:id="@+id/tv_title_cfc"
        android:layout_width="wrap_content"
        android:minWidth="500dp"
        android:layout_height="wrap_content"
        android:textColor="@color/x_theme_text_03"
        android:textSize="@dimen/x_font_body_03_size"
        android:text="@string/unknown"
        app:layout_constraintStart_toStartOf="@id/tv_title_vin"
        app:layout_constraintTop_toBottomOf="@id/tv_title_vin" />

    <com.xiaopeng.xui.view.XView
        android:layout_width="2dp"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:background="@color/separator_line_bg"
        app:layout_constraintStart_toEndOf="@id/tv_title_vin"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- 工位 -->
    <com.xiaopeng.xui.widget.XTextView
        android:id="@+id/tv_title_station"
        android:layout_width="wrap_content"
        android:minWidth="240dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/factory_title_margin_separator"
        android:textColor="@color/x_theme_text_01"
        android:textSize="@dimen/x_font_title_02_size"
        android:fontFamily="@string/x_font_typeface_bold"
        android:text="@string/factory_title_station"
        app:layout_constraintStart_toEndOf="@id/tv_title_vin"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <com.xiaopeng.xui.view.XView
        android:layout_width="2dp"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:layout_marginStart="@dimen/factory_title_margin_separator"
        android:background="@color/separator_line_bg"
        app:layout_constraintStart_toEndOf="@id/tv_title_station"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- 应用版本-->
    <com.xiaopeng.xui.widget.XTextView
        android:id="@+id/tv_title_app_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:ellipsize="end"
        android:layout_marginTop="16dp"
        android:layout_marginStart="80dp"
        android:textColor="@color/x_theme_text_03"
        android:textSize="@dimen/x_font_body_03_size"
        android:text="@string/unknown"
        app:layout_constraintStart_toEndOf="@id/tv_title_station"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 脚本库更新日期 -->
    <com.xiaopeng.xui.widget.XTextView
        android:id="@+id/tv_title_script_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/x_theme_text_03"
        android:textSize="@dimen/x_font_body_03_size"
        android:text="@string/unknown"
        app:layout_constraintStart_toStartOf="@id/tv_title_app_version"
        app:layout_constraintTop_toBottomOf="@id/tv_title_app_version" />

    <!-- 关闭按钮 -->
    <com.xiaopeng.xui.widget.XImageButton
        android:id="@+id/ib_title_close"
        style="@style/XButton.Icon.Large"
        android:layout_marginEnd="10dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/x_ic_small_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.xiaopeng.xui.widget.XButton
        android:id="@+id/btn_screen_record"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:text="屏幕录制"
        android:textSize="@dimen/x_font_body_03_size"
        android:background="@android:color/holo_blue_light"
        android:textColor="@android:color/white"
        android:padding="12dp"
        app:layout_constraintEnd_toStartOf="@id/btn_reload_task"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.xiaopeng.xui.widget.XButton
        android:id="@+id/btn_reload_task"
        style="@style/XButton.V5.Real.Small.Primary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="40dp"
        android:text="@string/autotest_task_refresh"
        android:textSize="@dimen/x_font_body_03_size"
        app:layout_constraintEnd_toStartOf="@id/ib_title_close"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</com.xiaopeng.xui.widget.XConstraintLayout>