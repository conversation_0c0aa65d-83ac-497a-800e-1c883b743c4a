package com.xiaopeng.xpautotest.utils;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.LocalSocket;
import android.net.LocalSocketAddress;
import android.util.Log;

import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * minicap数据流捕获器
 * 负责连接minicap本地套接字，解析数据流并提取图像帧
 */
public class MinicapDataCapture {
    private static final String TAG = "MinicapDataCapture";
    private static final String SOCKET_NAME = "minicap";
    
    private LocalSocket socket;
    private InputStream inputStream;
    private boolean isCapturing = false;
    private Thread captureThread;
    private FrameCallback frameCallback;
    
    // Banner信息
    private int bannerVersion;
    private int bannerLength;
    private int bannerPid;
    private int bannerRealWidth;
    private int bannerRealHeight;
    private int bannerVirtualWidth;
    private int bannerVirtualHeight;
    private int bannerOrientation;
    private int bannerQuirks;
    
    public interface FrameCallback {
        void onFrameReceived(Bitmap frame);
        void onError(String error);
    }
    
    public MinicapDataCapture(FrameCallback callback) {
        this.frameCallback = callback;
    }
    
    /**
     * 开始捕获数据流
     */
    public boolean startCapture() {
        if (isCapturing) {
            Log.w(TAG, "Already capturing");
            return true;
        }
        
        try {
            // 连接到minicap本地套接字
            socket = new LocalSocket();
            socket.connect(new LocalSocketAddress(SOCKET_NAME, LocalSocketAddress.Namespace.ABSTRACT));
            inputStream = socket.getInputStream();
            
            Log.i(TAG, "Connected to minicap socket");
            
            isCapturing = true;
            
            // 启动捕获线程
            captureThread = new Thread(this::captureLoop);
            captureThread.start();
            
            return true;
            
        } catch (IOException e) {
            Log.e(TAG, "Error connecting to minicap socket", e);
            cleanup();
            return false;
        }
    }
    
    /**
     * 停止捕获
     */
    public void stopCapture() {
        isCapturing = false;
        
        if (captureThread != null) {
            try {
                captureThread.interrupt();
                captureThread.join(3000);
            } catch (InterruptedException e) {
                Log.e(TAG, "Error stopping capture thread", e);
            }
        }
        
        cleanup();
    }
    
    /**
     * 数据捕获循环
     */
    private void captureLoop() {
        try {
            // 首先读取banner信息
            if (!readBanner()) {
                frameCallback.onError("Failed to read minicap banner");
                return;
            }
            
            Log.i(TAG, String.format("Minicap banner: %dx%d@%dx%d/%d",
                bannerRealWidth, bannerRealHeight,
                bannerVirtualWidth, bannerVirtualHeight,
                bannerOrientation));
            
            // 循环读取帧数据
            while (isCapturing && !Thread.currentThread().isInterrupted()) {
                if (!readFrame()) {
                    break;
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error in capture loop", e);
            frameCallback.onError("Capture loop error: " + e.getMessage());
        } finally {
            cleanup();
        }
    }
    
    /**
     * 读取minicap banner信息
     */
    private boolean readBanner() throws IOException {
        byte[] bannerBuffer = new byte[24];
        int bytesRead = 0;
        
        while (bytesRead < bannerBuffer.length) {
            int read = inputStream.read(bannerBuffer, bytesRead, bannerBuffer.length - bytesRead);
            if (read == -1) {
                Log.e(TAG, "Unexpected end of stream while reading banner");
                return false;
            }
            bytesRead += read;
        }
        
        ByteBuffer buffer = ByteBuffer.wrap(bannerBuffer).order(ByteOrder.LITTLE_ENDIAN);
        
        bannerVersion = buffer.get() & 0xFF;
        bannerLength = buffer.get() & 0xFF;
        bannerPid = buffer.getInt();
        bannerRealWidth = buffer.getInt();
        bannerRealHeight = buffer.getInt();
        bannerVirtualWidth = buffer.getInt();
        bannerVirtualHeight = buffer.getInt();
        bannerOrientation = buffer.get() & 0xFF;
        bannerQuirks = buffer.get() & 0xFF;
        
        return true;
    }
    
    /**
     * 读取单个帧数据
     */
    private boolean readFrame() throws IOException {
        // 读取帧长度（4字节）
        byte[] lengthBuffer = new byte[4];
        int bytesRead = 0;
        
        while (bytesRead < lengthBuffer.length) {
            int read = inputStream.read(lengthBuffer, bytesRead, lengthBuffer.length - bytesRead);
            if (read == -1) {
                Log.d(TAG, "End of stream reached");
                return false;
            }
            bytesRead += read;
        }
        
        ByteBuffer lengthByteBuffer = ByteBuffer.wrap(lengthBuffer).order(ByteOrder.LITTLE_ENDIAN);
        int frameLength = lengthByteBuffer.getInt();
        
        if (frameLength <= 0 || frameLength > 10 * 1024 * 1024) { // 最大10MB
            Log.e(TAG, "Invalid frame length: " + frameLength);
            return false;
        }
        
        // 读取帧数据
        byte[] frameBuffer = new byte[frameLength];
        bytesRead = 0;
        
        while (bytesRead < frameLength) {
            int read = inputStream.read(frameBuffer, bytesRead, frameLength - bytesRead);
            if (read == -1) {
                Log.e(TAG, "Unexpected end of stream while reading frame");
                return false;
            }
            bytesRead += read;
        }
        
        // 解码为Bitmap
        try {
            Bitmap bitmap = BitmapFactory.decodeByteArray(frameBuffer, 0, frameLength);
            if (bitmap != null) {
                frameCallback.onFrameReceived(bitmap);
            } else {
                Log.w(TAG, "Failed to decode frame to bitmap");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error decoding frame", e);
        }
        
        return true;
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        try {
            if (inputStream != null) {
                inputStream.close();
                inputStream = null;
            }
            
            if (socket != null) {
                socket.close();
                socket = null;
            }
        } catch (IOException e) {
            Log.e(TAG, "Error during cleanup", e);
        }
    }
    
    /**
     * 获取屏幕尺寸信息
     */
    public int getRealWidth() {
        return bannerRealWidth;
    }
    
    public int getRealHeight() {
        return bannerRealHeight;
    }
    
    public int getVirtualWidth() {
        return bannerVirtualWidth;
    }
    
    public int getVirtualHeight() {
        return bannerVirtualHeight;
    }
    
    public int getOrientation() {
        return bannerOrientation;
    }
}
