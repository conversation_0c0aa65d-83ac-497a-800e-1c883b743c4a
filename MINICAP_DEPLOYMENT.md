# Minicap部署配置更新

## 📋 更新内容

### 1. 部署目录变更
- **旧路径**: `/data/local/tmp/`
- **新路径**: `/sdcard/AutoTest/minicap/`

### 2. 代码简化
- 移除了不必要的`startMinicapFromSystem`方法
- 简化了文件部署逻辑
- 直接使用我们自己拷贝的minicap文件

### 3. 权限优化
- 使用sdcard目录，避免系统目录权限问题
- 自动创建部署目录
- 简化文件复制过程

## 🔧 新的部署流程

### 自动部署
```bash
# Windows
deploy_minicap.bat

# Linux/Mac
./deploy_minicap.sh
```

### 手动部署
```bash
# 1. 创建目录
adb shell mkdir -p /sdcard/AutoTest/minicap

# 2. 推送文件 (以arm64-v8a为例)
adb push app/src/main/assets/minicap/arm64-v8a/minicap /sdcard/AutoTest/minicap/
adb push app/src/main/assets/minicap/arm64-v8a/minicap.so /sdcard/AutoTest/minicap/

# 3. 设置权限
adb shell chmod 755 /sdcard/AutoTest/minicap/minicap
adb shell chmod 644 /sdcard/AutoTest/minicap/minicap.so

# 4. 验证
adb shell ls -la /sdcard/AutoTest/minicap/*
```

### 测试minicap
```bash
adb shell "cd /sdcard/AutoTest/minicap && LD_LIBRARY_PATH=. ./minicap -P 1920x1080@1920x1080/0 -S"
```

## 📁 文件结构

### Assets目录
```
app/src/main/assets/minicap/
├── arm64-v8a/
│   ├── minicap      # ARM64架构二进制文件
│   └── minicap.so   # ARM64架构共享库
├── armeabi-v7a/
│   ├── minicap      # ARMv7架构二进制文件
│   └── minicap.so   # ARMv7架构共享库
├── x86/
│   ├── minicap      # x86架构二进制文件
│   └── minicap.so   # x86架构共享库
└── x86_64/
    ├── minicap      # x86_64架构二进制文件
    └── minicap.so   # x86_64架构共享库
```

### 设备部署目录
```
/sdcard/AutoTest/minicap/
├── minicap          # 可执行文件
└── minicap.so       # 共享库
```

## 🎯 优势

### 1. 权限友好
- sdcard目录通常有读写权限
- 避免系统目录的权限限制
- 不需要root权限

### 2. 代码简洁
- 移除了复杂的系统minicap检查
- 统一使用自己部署的文件
- 减少了代码复杂度

### 3. 调试方便
- 文件路径更直观
- 容易手动验证和测试
- 便于问题排查

## 🔍 验证方法

### 1. 检查文件是否存在
```bash
adb shell ls -la /sdcard/AutoTest/minicap/
```

### 2. 检查文件权限
```bash
adb shell ls -la /sdcard/AutoTest/minicap/minicap
```

### 3. 测试minicap运行
```bash
adb shell "cd /sdcard/AutoTest/minicap && ./minicap -P 1920x1080@1920x1080/0 -S"
```

### 4. 检查应用日志
```bash
adb logcat -s MinicapManager MinicapValidator
```

## 📱 应用行为

### 成功部署
- 应用自动创建`/sdcard/AutoTest/minicap/`目录
- 从assets复制对应架构的文件
- 设置正确的文件权限
- 启动minicap进程

### 部署失败
- 记录详细错误信息
- 自动切换到模拟模式
- 性能监控功能仍然可用
- 提供用户友好的错误提示

## 🚀 下一步

1. **获取minicap文件**: 从STF项目下载对应架构的文件
2. **放置到assets**: 替换PLACEHOLDER.txt文件
3. **运行部署脚本**: 使用自动化脚本部署
4. **测试功能**: 启动应用测试屏幕录制

---

**注意**: 新的部署方式更加稳定和用户友好，避免了系统目录的权限问题。
