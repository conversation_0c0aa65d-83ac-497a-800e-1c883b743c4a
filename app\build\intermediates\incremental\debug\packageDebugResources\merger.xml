<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\WorkSpace\code\xpAutoTest\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\WorkSpace\code\xpAutoTest\app\src\main\res"><file name="bg_recyclerview_scrollbar" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\bg_recyclerview_scrollbar.xml" qualifiers="" type="drawable"/><file name="bg_test_result_table" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\bg_test_result_table.xml" qualifiers="" type="drawable"/><file name="bg_topic_index" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\bg_topic_index.xml" qualifiers="" type="drawable"/><file name="bg_topic_item" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\bg_topic_item.xml" qualifiers="" type="drawable"/><file name="border" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\border.xml" qualifiers="" type="drawable"/><file name="cell_border" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\cell_border.xml" qualifiers="" type="drawable"/><file name="floating_window_background" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\floating_window_background.xml" qualifiers="" type="drawable"/><file name="ic_debug" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\ic_debug.png" qualifiers="" type="drawable"/><file name="ic_launcher" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\ic_launcher.png" qualifiers="" type="drawable"/><file name="ic_launcher2" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\ic_launcher2.png" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_record_red" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\ic_record_red.xml" qualifiers="" type="drawable"/><file name="ic_screen_record" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\ic_screen_record.xml" qualifiers="" type="drawable"/><file name="ic_stop_record" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable\ic_stop_record.xml" qualifiers="" type="drawable"/><file name="bwater_top_input" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable-v24\bwater_top_input.9.png" qualifiers="v24" type="drawable"/><file name="ic_launcher_foreground" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="activity_performance_report" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\activity_performance_report.xml" qualifiers="" type="layout"/><file name="activity_screen_record_test" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\activity_screen_record_test.xml" qualifiers="" type="layout"/><file name="at_activity_factory" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\at_activity_factory.xml" qualifiers="" type="layout"/><file name="at_floating_debug_mode" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\at_floating_debug_mode.xml" qualifiers="" type="layout"/><file name="at_fragment_factory_sub_directory" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\at_fragment_factory_sub_directory.xml" qualifiers="" type="layout"/><file name="at_fragment_loading_dialog" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\at_fragment_loading_dialog.xml" qualifiers="" type="layout"/><file name="at_fragment_tab" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\at_fragment_tab.xml" qualifiers="" type="layout"/><file name="at_layout_dialog_loading" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\at_layout_dialog_loading.xml" qualifiers="" type="layout"/><file name="at_layout_factory_component_item" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\at_layout_factory_component_item.xml" qualifiers="" type="layout"/><file name="at_layout_factory_tab_item" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\at_layout_factory_tab_item.xml" qualifiers="" type="layout"/><file name="at_layout_factory_title" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\at_layout_factory_title.xml" qualifiers="" type="layout"/><file name="floating_recording_window" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\floating_recording_window.xml" qualifiers="" type="layout"/><file name="ly_floating_window" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\ly_floating_window.xml" qualifiers="" type="layout"/><file name="img_topic_item_cover_default" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap\img_topic_item_cover_default.png" qualifiers="" type="mipmap"/><file name="ic_launcher" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_maintain" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-mdpi\icon_maintain.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_status_execute" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-mdpi\icon_status_execute.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_status_failure" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-mdpi\icon_status_failure.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_status_pending" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-mdpi\icon_status_pending.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_status_skipped" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-mdpi\icon_status_skipped.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_status_success" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-mdpi\icon_status_success.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_xlogo" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-mdpi\icon_xlogo.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_xlogo" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-night-mdpi\icon_xlogo.png" qualifiers="night-mdpi-v8" type="mipmap"/><file name="ic_launcher" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="bwater_top_input" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-xxhdpi\bwater_top_input.9.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\values\colors.xml" qualifiers=""><color name="colorPrimary">#008577</color><color name="colorPrimaryDark">#00574B</color><color name="colorAccent">#D81B60</color><color name="separator_line_bg">#A3BBC2D1</color><color name="bg_status_pending">@color/x_theme_primary_neutral_disable</color><color name="bg_status_executing">@color/x_theme_primary_neutral_normal</color><color name="bg_status_execute_success">@color/x_theme_primary_special_normal</color><color name="bg_status_execute_failure">@color/x_theme_primary_negative_normal</color></file><file path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="layout_search_width">330dp</dimen><dimen name="layout_search_height">80dp</dimen><dimen name="layout_search_margin_top">12dp</dimen><dimen name="layout_search_margin_end_primary">16dp</dimen><dimen name="layout_search_margin_end_fullScreen">280dp</dimen><dimen name="layout_search_image_margin_start">20dp</dimen><dimen name="layout_search_text_margin_start">64dp</dimen><dimen name="layout_fragment_container_margin_start">0dp</dimen><dimen name="layout_fragment_container_margin_end">0dp</dimen><dimen name="layout_fragment_container_margin_top">144dp</dimen><dimen name="layout_fragment_container_margin_bottom">0dp</dimen><dimen name="exo_h5_btn_margin_end">48dp</dimen><dimen name="exo_h5_btn_width">240dp</dimen><dimen name="exo_menu_margin_width">448dp</dimen><dimen name="exo_menu_margin_start">48dp</dimen><dimen name="exo_menu_margin_end">24dp</dimen><dimen name="exo_menu_margin_Top">144dp</dimen><dimen name="recyclerview_padding_left_ivi_immersion">112dp</dimen><dimen name="recyclerview_padding_right_ivi_immersion">160dp</dimen><dimen name="recyclerview_padding_left_ivi_primary">0dp</dimen><dimen name="recyclerview_padding_right_ivi_primary">48dp</dimen><dimen name="recyclerview_scrollbar_bg_width">24dp</dimen><dimen name="recyclerview_scrollbar_insert">12dp</dimen><dimen name="recyclerview_scrollbar_width">12dp</dimen><dimen name="topic_fragment_padding_end">0dp</dimen><dimen name="topic_index_bg_radius">18dp</dimen><dimen name="topic_item_width_res">490dp</dimen><dimen name="topic_item_width_ivi_immersion">460dp</dimen><dimen name="topic_item_width_ivi_primary">383dp</dimen><dimen name="topic_item_height">343dp</dimen><dimen name="topic_item_image_width_res">490dp</dimen><dimen name="topic_item_image_width_ivi_immersion">492dp</dimen><dimen name="topic_item_image_width_ivi_primary">422dp</dimen><dimen name="topic_item_image_height">240dp</dimen><dimen name="topic_item_play_size">40dp</dimen><dimen name="topic_item_play_margin_start">33dp</dimen><dimen name="topic_item_play_margin_top">172dp</dimen><dimen name="topic_item_text_margin_top">240dp</dimen><dimen name="topic_item_out_rect_spacing">40dp</dimen><dimen name="title_item_out_rect_bottom">12dp</dimen><dimen name="title_item_out_rect_top_middle">4dp</dimen><dimen name="title_item_out_rect_top_first">18dp</dimen><dimen name="title_item_folder_external_size">36dp</dimen><dimen name="dp_100">100dp</dimen><dimen name="dp_80">100dp</dimen><dimen name="dp_60">60dp</dimen><dimen name="title_height">100dp</dimen><dimen name="title_button_width">80dp</dimen><dimen name="title_button_height">60dp</dimen><dimen name="fragment_right_padding">20dp</dimen><dimen name="factory_footer_height">120dp</dimen><dimen name="factory_fragment_left_width">450dp</dimen><dimen name="factory_title_image_width">60dp</dimen><dimen name="factory_title_image_height">60dp</dimen><dimen name="factory_title_margin_separator">40dp</dimen><dimen name="factory_tab_height">90dp</dimen><dimen name="directory_list_height">@dimen/dp_80</dimen><dimen name="button_component_width">100dp</dimen><dimen name="button_component_height">60dp</dimen></file><file path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#3D6ADC</color></file><file path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">自动化测试</string><string name="CHOOSE_PRE_INSTALLED_MODEL_KEY">CHOOSE_PRE_INSTALLED_MODEL_KEY</string><string name="ENABLE_CUSTOM_SETTINGS_KEY">ENABLE_CUSTOM_SETTINGS_KEY</string><string name="MODEL_PATH_KEY">MODEL_PATH_KEY</string><string name="LABEL_PATH_KEY">LABEL_PATH_KEY</string><string name="IMAGE_PATH_KEY">IMAGE_PATH_KEY</string><string name="CPU_THREAD_NUM_KEY">CPU_THREAD_NUM_KEY</string><string name="CPU_POWER_MODE_KEY">CPU_POWER_MODE_KEY</string><string name="INPUT_COLOR_FORMAT_KEY">INPUT_COLOR_FORMAT_KEY</string><string name="INPUT_SHAPE_KEY">INPUT_SHAPE_KEY</string><string name="INPUT_MEAN_KEY">INPUT_MEAN_KEY</string><string name="INPUT_STD_KEY">INPUT_STD_KEY</string><string name="SCORE_THRESHOLD_KEY">SCORE_THRESHOLD_KEY</string><string name="MODEL_PATH_DEFAULT">models/ocr_v2_for_cpu</string><string name="LABEL_PATH_DEFAULT">labels/ppocr_keys_v1.txt</string><string name="IMAGE_PATH_DEFAULT">images/1.png</string><string name="CPU_THREAD_NUM_DEFAULT">4</string><string name="CPU_POWER_MODE_DEFAULT">LITE_POWER_HIGH</string><string name="INPUT_COLOR_FORMAT_DEFAULT">BGR</string><string name="INPUT_SHAPE_DEFAULT">1,3,960</string><string name="INPUT_MEAN_DEFAULT">0.485, 0.456, 0.406</string><string name="INPUT_STD_DEFAULT">0.229,0.224,0.225</string><string name="SCORE_THRESHOLD_DEFAULT">0.1</string><string name="case_title">自动化测试用例列表</string><string name="case_run">开始执行</string><string name="web_search">搜索</string><string name="web_search_hint">请输入搜索内容</string><string name="loading">加载中…</string><string name="result_title">测试结果</string><string name="exo_h5_detail" translatable="false">开始测试</string><string name="accessibility_service_description">无障碍服务</string><string name="autotest_task_refresh">刷新任务</string><string name="testcase_table_title_status">状态</string><string name="testcase_table_title_name">测试项</string><string name="testcase_table_title_result">测试结果</string><string name="testcase_table_title_operation">操作</string><string name="autotest_title">整车自动化测试</string><string name="autotest_title_factory">整车自动化点检</string><string name="unknown">--</string><string name="factory_title_station">进度：%s</string><string name="factory_title_upload_event">事件上报</string><string name="factory_title_app_version">应用版本：%s</string><string name="factory_title_script_date">任务名称：%s</string><string name="factory_mode_execute_all_components">开始测试</string><string name="component_view_log">日志</string><string name="component_view_history_result">结果</string><string name="component_execute">执行</string><string name="component_abort">终止</string><string name="function_status_pending">等待执行</string><string name="function_status_executing">正在执行</string><string name="function_status_execute_success">执行成功</string><string name="function_status_execute_failure">执行失败</string><string name="function_status_executing_desc">执行中...</string><string name="retry_execute_confirm_title">执行场景测试</string><string name="retry_execute_confirm_message">确定要执行“%s”场景下全部的用例吗？</string><string name="retry_execute_confirm_positive_button">确定</string><string name="retry_execute_confirm_negative_button">取消</string><string name="factory_loading_error_title">初始化异常</string><string name="factory_loading_error_button">退出</string><string name="dialog_positive_button">确定</string><string name="error_unknown">未知错误</string><string name="error_network_no_connection">网络连接异常，请检查网络</string><string name="error_network_timeout">网络连接超时，请稍后重试</string><string name="error_network_generic">网络连接异常，请稍后重试</string><string name="error_network_no_response">网络连接异常，请稍后重试</string><string name="error_network_no_data">暂无数据</string><string name="error_network_no_wifi">WIFI未连接到XP-AUTO</string><string name="error_api_server_error">测试平台异常</string><string name="error_api_server_unknown">测试平台未知错误</string><string name="error_api_not_found">测试平台任务不存在</string><string name="error_api_empty_param">请求测试平台参数错误</string><string name="error_api_register_fail">注册到测试平台失败</string><string name="error_api_register_timeout">注册到测试平台超时</string><string name="error_api_register_unknown">注册到测试平台异常</string><string name="error_api_register_no_response">注册到测试平台无响应</string><string name="error_api_register_no_task">当前无测试任务，请先在测试平台创建测试任务</string><string name="error_api_download_script">测试脚本下载失败</string><string name="error_api_download_script_timeout">测试脚本下载超时</string><string name="error_api_download_script_unknown">测试脚本下载异常</string><string name="error_api_download_script_write">测试脚本解析异常</string><string name="error_api_execution_stop">测试脚本解析异常</string><string name="error_auth_token_expired">认证错误</string><string name="error_load_script">加载测试脚本失败</string><string name="executor_finish_confirm_title">结束自动化测试确认</string><string name="executor_finish_confirm_message">确定要停止自动化测试吗？</string><string name="executor_finish_confirm_stop_button">停止</string><string name="executor_finish_confirm_continue_button">继续</string><string name="reload_task_confirm_title">刷新任务确认</string><string name="reload_task_confirm_message">确定清空数据并重新下载任务吗？</string><string name="reload_task_confirm_positive_button">确定</string><string name="reload_task_confirm_negative_button">取消</string><string name="executor_no_task">"当前无测试任务，请先在平台配置测试任务！"</string><string name="executor_debug_mode_title">脚本调试模式</string><string name="executor_debug_confirm_title">结束调试模式确认</string><string name="executor_debug_confirm_message">确定要结束自动化的调试模式吗？</string></file><file path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\values\styles.xml" qualifiers=""><style name="autotest_AppTheme" parent="@style/XAppTheme">
        <item name="android:fontFamily">@null</item>
    </style><style name="TableHeaderLine">
        <item name="android:layout_width">1dp</item>
        <item name="android:layout_height">fill_parent</item>
        <item name="android:background">@color/separator_line_bg</item>
    </style><style name="TableHeaderHorizontalLine">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@color/separator_line_bg</item>
    </style></file><file path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="separator_line_bg">#33757D8D</color></file><file name="network_security_config" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file name="service_config" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\xml\service_config.xml" qualifiers="" type="xml"/><file name="activity_performance_report_simple" path="E:\WorkSpace\code\xpAutoTest\app\src\main\res\layout\activity_performance_report_simple.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\WorkSpace\code\xpAutoTest\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\WorkSpace\code\xpAutoTest\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\WorkSpace\code\xpAutoTest\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\WorkSpace\code\xpAutoTest\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>