<com.xiaopeng.xui.widget.XConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.xiaopeng.xui.widget.XConstraintLayout
        android:id="@+id/ll_item_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/directory_list_height"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.xiaopeng.xui.widget.XLinearLayout
            android:id="@+id/ll_status"
            android:layout_width="200dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.xiaopeng.xui.widget.XImageView
                android:id="@+id/iv_status"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginStart="45dp"
                android:layout_marginTop="10dp" />

        </com.xiaopeng.xui.widget.XLinearLayout>

        <com.xiaopeng.xui.view.XView
            style="@style/TableHeaderLine"
            app:layout_constraintEnd_toStartOf="@id/ll_status" />

        <com.xiaopeng.xui.widget.XTextView
            android:id="@+id/tv_name"
            android:layout_width="600dp"
            android:layout_height="match_parent"
            android:textColor="@color/x_theme_text_02"
            android:textSize="@dimen/x_font_body_03_size"
            android:textAlignment="center"
            android:gravity="center"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/ll_status" />

        <com.xiaopeng.xui.view.XView
            style="@style/TableHeaderLine"
            app:layout_constraintEnd_toStartOf="@id/tv_name" />

        <com.xiaopeng.xui.view.XView
            style="@style/TableHeaderLine"
            app:layout_constraintStart_toEndOf="@id/tv_name" />

<!--        <com.xiaopeng.xui.widget.XButton-->
<!--            android:id="@+id/button_view_log"-->
<!--            style="@style/XButton.V5.Real.Small.Primary"-->
<!--            android:layout_width="150dp"-->
<!--            android:layout_height="@dimen/button_component_height"-->
<!--            android:layout_marginStart="20dp"-->
<!--            android:text="@string/component_view_log"-->
<!--            android:textSize="@dimen/x_font_body_02_size"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintStart_toEndOf="@id/tv_name"/>-->

<!--        <com.xiaopeng.xui.widget.XButton-->
<!--            android:id="@+id/button_view_history_result"-->
<!--            style="@style/XButton.V5.Real.Small.Primary"-->
<!--            android:layout_width="150dp"-->
<!--            android:layout_height="@dimen/button_component_height"-->
<!--            android:layout_marginStart="20dp"-->
<!--            android:text="@string/component_view_history_result"-->
<!--            android:textSize="@dimen/x_font_body_02_size"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintStart_toEndOf="@id/button_view_log"/>-->

        <com.xiaopeng.xui.widget.XButton
            android:id="@+id/button_execute"
            style="@style/XButton.V5.Real.Small.Primary"
            android:layout_width="150dp"
            android:layout_height="@dimen/button_component_height"
            android:layout_marginStart="20dp"
            android:text="@string/component_execute"
            android:textSize="@dimen/x_font_body_02_size"
            android:layout_gravity="center_vertical"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_name" />

<!--        <com.xiaopeng.xui.widget.XButton-->
<!--            android:id="@+id/button_abort"-->
<!--            style="@style/XButton.V5.Real.Small.Primary"-->
<!--            android:layout_width="150dp"-->
<!--            android:layout_height="@dimen/button_component_height"-->
<!--            android:layout_marginStart="20dp"-->
<!--            android:text="@string/component_abort"-->
<!--            android:textSize="@dimen/x_font_body_02_size"-->
<!--            android:layout_gravity="center_vertical"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintStart_toEndOf="@id/button_execute"/>-->

        <com.xiaopeng.xui.view.XView
            style="@style/TableHeaderLine"
            app:layout_constraintEnd_toEndOf="parent"/>

    </com.xiaopeng.xui.widget.XConstraintLayout>

    <com.xiaopeng.xui.view.XView
        style="@style/TableHeaderHorizontalLine"
        app:layout_constraintBottom_toBottomOf="parent" />

</com.xiaopeng.xui.widget.XConstraintLayout>