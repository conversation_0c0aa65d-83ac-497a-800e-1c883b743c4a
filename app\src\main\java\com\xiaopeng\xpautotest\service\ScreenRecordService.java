package com.xiaopeng.xpautotest.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.net.Uri;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import android.provider.MediaStore;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Surface;
import android.view.WindowManager;

import androidx.core.app.NotificationCompat;

import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.ui.MainActivity;
import com.xiaopeng.xpautotest.utils.MinicapDataCapture;
import com.xiaopeng.xpautotest.utils.MinicapManager;
import com.xiaopeng.xpautotest.utils.PerformanceMonitor;

import java.io.File;
import java.io.FileDescriptor;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 屏幕录制服务
 * 集成minicap数据流捕获、MediaCodec编码和MediaMuxer文件存储
 */
public class ScreenRecordService extends Service {
    private static final String TAG = "ScreenRecordService";
    
    // 服务相关
    private static final String CHANNEL_ID = "screen_record_channel";
    private static final int NOTIFICATION_ID = 1001;
    
    // 录制参数
    private static final String MIME_TYPE = "video/avc";
    private static final int FRAME_RATE = 30;
    private static final int I_FRAME_INTERVAL = 1;
    private static final int BIT_RATE_MULTIPLIER = 6; // 比特率倍数
    
    // 组件
    private MinicapManager minicapManager;
    private MinicapDataCapture dataCapture;
    private PerformanceMonitor performanceMonitor;
    private MediaCodec encoder;
    private MediaMuxer muxer;
    private Surface inputSurface;
    
    // 录制状态
    private boolean isRecording = false;
    private int videoTrackIndex = -1;
    private boolean muxerStarted = false;
    
    // 屏幕参数
    private int screenWidth;
    private int screenHeight;
    private int bitRate;
    
    // 帧处理
    private BlockingQueue<Bitmap> frameQueue;
    private Thread encodingThread;
    
    // 文件相关
    private File outputFile;
    private Uri outputUri;
    
    public class ScreenRecordBinder extends Binder {
        public ScreenRecordService getService() {
            return ScreenRecordService.this;
        }
    }
    
    private final IBinder binder = new ScreenRecordBinder();
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "ScreenRecordService created");
        
        // 初始化组件
        minicapManager = new MinicapManager(this);
        performanceMonitor = new PerformanceMonitor(this);
        frameQueue = new LinkedBlockingQueue<>();
        
        // 获取屏幕尺寸
        getScreenDimensions();
        
        // 创建通知渠道
        createNotificationChannel();
        
        // 设置性能监控回调
        performanceMonitor.setPerformanceCallback(data -> {
            Log.i(TAG, "Performance: CPU=" + String.format("%.2f", data.cpuUsage) + 
                "%, Memory=" + data.memoryPss + "KB");
        });
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        String action = intent != null ? intent.getAction() : null;
        
        if ("START_RECORDING".equals(action)) {
            startRecording();
        } else if ("STOP_RECORDING".equals(action)) {
            stopRecording();
        }
        
        return START_NOT_STICKY;
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "ScreenRecordService destroyed");
        
        stopRecording();
        performanceMonitor.stopMonitoring();
        minicapManager.cleanup();
    }
    
    /**
     * 开始录制
     */
    public boolean startRecording() {
        if (isRecording) {
            Log.w(TAG, "Already recording");
            return false;
        }
        
        try {
            Log.i(TAG, "Starting screen recording");
            
            // 启动前台服务
            startForeground(NOTIFICATION_ID, createNotification("正在录制屏幕..."));
            
            // 部署minicap
            if (!minicapManager.deployMinicap()) {
                Log.e(TAG, "Failed to deploy minicap");
                return false;
            }
            
            // 启动minicap
            if (!minicapManager.startMinicap(screenWidth, screenHeight, "minicap")) {
                Log.w(TAG, "Failed to start minicap, will use simulation mode");
                // 不返回false，继续使用模拟模式
            }
            
            // 等待minicap启动
            Thread.sleep(2000);
            
            // 准备输出文件
            if (!prepareOutputFile()) {
                Log.e(TAG, "Failed to prepare output file");
                return false;
            }
            
            // 初始化编码器
            if (!initializeEncoder()) {
                Log.e(TAG, "Failed to initialize encoder");
                return false;
            }
            
            // 启动数据捕获
            if (minicapManager.isMinicapRunning()) {
                // 使用真实的minicap数据捕获
                dataCapture = new MinicapDataCapture(new MinicapDataCapture.FrameCallback() {
                    @Override
                    public void onFrameReceived(Bitmap frame) {
                        if (isRecording && frameQueue.size() < 10) { // 限制队列大小
                            frameQueue.offer(frame);
                        }
                    }

                    @Override
                    public void onError(String error) {
                        Log.e(TAG, "Minicap capture error: " + error);
                        // 切换到模拟模式
                        startSimulationMode();
                    }
                });

                if (!dataCapture.startCapture()) {
                    Log.w(TAG, "Failed to start minicap capture, switching to simulation mode");
                    startSimulationMode();
                }
            } else {
                // 直接使用模拟模式
                Log.i(TAG, "Starting simulation mode");
                startSimulationMode();
            }
            
            // 启动编码线程
            startEncodingThread();
            
            // 启动性能监控
            performanceMonitor.startMonitoring();
            
            isRecording = true;
            Log.i(TAG, "Screen recording started successfully");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting recording", e);
            stopRecording();
            return false;
        }
    }
    
    /**
     * 停止录制
     */
    public void stopRecording() {
        if (!isRecording) {
            return;
        }
        
        Log.i(TAG, "Stopping screen recording");
        isRecording = false;
        
        try {
            // 停止数据捕获
            if (dataCapture != null) {
                dataCapture.stopCapture();
                dataCapture = null;
            }
            
            // 停止编码线程
            if (encodingThread != null) {
                encodingThread.interrupt();
                try {
                    encodingThread.join(3000);
                } catch (InterruptedException e) {
                    Log.e(TAG, "Error stopping encoding thread", e);
                }
                encodingThread = null;
            }
            
            // 停止编码器
            stopEncoder();
            
            // 停止minicap
            minicapManager.stopMinicap();
            
            // 停止性能监控
            performanceMonitor.stopMonitoring();
            
            // 更新通知
            NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            if (notificationManager != null) {
                notificationManager.notify(NOTIFICATION_ID, createNotification("录制完成"));
            }
            
            Log.i(TAG, "Screen recording stopped. File saved: " + 
                (outputFile != null ? outputFile.getAbsolutePath() : "unknown"));
            
        } catch (Exception e) {
            Log.e(TAG, "Error stopping recording", e);
        } finally {
            stopForeground(false);
        }
    }
    
    /**
     * 获取屏幕尺寸
     */
    private void getScreenDimensions() {
        WindowManager windowManager = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
        if (windowManager != null) {
            DisplayMetrics metrics = new DisplayMetrics();
            windowManager.getDefaultDisplay().getRealMetrics(metrics);
            screenWidth = metrics.widthPixels;
            screenHeight = metrics.heightPixels;
            bitRate = screenWidth * screenHeight * BIT_RATE_MULTIPLIER;
            
            Log.d(TAG, String.format("Screen dimensions: %dx%d, bitrate: %d", 
                screenWidth, screenHeight, bitRate));
        }
    }
    
    /**
     * 准备输出文件
     */
    private boolean prepareOutputFile() {
        try {
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            String fileName = "screen_record_" + timestamp + ".mp4";
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ 使用MediaStore
                ContentResolver resolver = getContentResolver();
                ContentValues contentValues = new ContentValues();
                contentValues.put(MediaStore.Video.Media.DISPLAY_NAME, fileName);
                contentValues.put(MediaStore.Video.Media.MIME_TYPE, "video/mp4");
                contentValues.put(MediaStore.Video.Media.RELATIVE_PATH, "Movies/ScreenRecords");
                
                outputUri = resolver.insert(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, contentValues);
                if (outputUri == null) {
                    Log.e(TAG, "Failed to create MediaStore entry");
                    return false;
                }
                
                Log.d(TAG, "Output URI: " + outputUri);
                return true;
                
            } else {
                // Android 9及以下使用文件路径
                File moviesDir = new File(getExternalFilesDir(null), "ScreenRecords");
                if (!moviesDir.exists() && !moviesDir.mkdirs()) {
                    Log.e(TAG, "Failed to create output directory");
                    return false;
                }
                
                outputFile = new File(moviesDir, fileName);
                Log.d(TAG, "Output file: " + outputFile.getAbsolutePath());
                return true;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error preparing output file", e);
            return false;
        }
    }

    /**
     * 初始化编码器
     */
    private boolean initializeEncoder() {
        try {
            // 创建MediaFormat
            MediaFormat format = MediaFormat.createVideoFormat(MIME_TYPE, screenWidth, screenHeight);
            format.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface);
            format.setInteger(MediaFormat.KEY_BIT_RATE, bitRate);
            format.setInteger(MediaFormat.KEY_FRAME_RATE, FRAME_RATE);
            format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, I_FRAME_INTERVAL);

            // 创建编码器
            encoder = MediaCodec.createEncoderByType(MIME_TYPE);
            encoder.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
            inputSurface = encoder.createInputSurface();
            encoder.start();

            // 创建MediaMuxer
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && outputUri != null) {
                FileDescriptor fd = getContentResolver().openFileDescriptor(outputUri, "w").getFileDescriptor();
                muxer = new MediaMuxer(fd, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
            } else if (outputFile != null) {
                muxer = new MediaMuxer(outputFile.getAbsolutePath(), MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
            } else {
                Log.e(TAG, "No valid output target");
                return false;
            }

            Log.d(TAG, "Encoder initialized successfully");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error initializing encoder", e);
            return false;
        }
    }

    /**
     * 启动编码线程
     */
    private void startEncodingThread() {
        encodingThread = new Thread(() -> {
            try {
                MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();

                while (isRecording && !Thread.currentThread().isInterrupted()) {
                    // 处理帧队列中的Bitmap
                    Bitmap frame = frameQueue.poll();
                    if (frame != null) {
                        // 这里需要将Bitmap转换为Surface输入
                        // 由于直接转换比较复杂，我们使用编码器的输出缓冲区
                        processFrame(frame);
                    }

                    // 处理编码器输出
                    drainEncoder(bufferInfo, false);

                    Thread.sleep(33); // ~30fps
                }

                // 最后一次drain
                drainEncoder(bufferInfo, true);

            } catch (Exception e) {
                Log.e(TAG, "Error in encoding thread", e);
            }
        });

        encodingThread.start();
    }

    /**
     * 处理单个帧
     */
    private void processFrame(Bitmap bitmap) {
        // 这里简化处理，实际应用中需要将Bitmap渲染到Surface
        // 由于minicap已经提供了压缩的JPEG数据，我们可以直接使用
        // 在实际实现中，可能需要使用OpenGL ES将Bitmap渲染到Surface
    }

    /**
     * 从编码器获取输出数据
     */
    private void drainEncoder(MediaCodec.BufferInfo bufferInfo, boolean endOfStream) {
        if (endOfStream) {
            encoder.signalEndOfInputStream();
        }

        while (true) {
            int outputBufferIndex = encoder.dequeueOutputBuffer(bufferInfo, 0);

            if (outputBufferIndex == MediaCodec.INFO_TRY_AGAIN_LATER) {
                if (!endOfStream) {
                    break;
                }
            } else if (outputBufferIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                if (muxerStarted) {
                    throw new RuntimeException("Format changed twice");
                }

                MediaFormat newFormat = encoder.getOutputFormat();
                videoTrackIndex = muxer.addTrack(newFormat);
                muxer.start();
                muxerStarted = true;

            } else if (outputBufferIndex >= 0) {
                ByteBuffer outputBuffer = encoder.getOutputBuffer(outputBufferIndex);

                if (bufferInfo.size != 0) {
                    if (!muxerStarted) {
                        throw new RuntimeException("Muxer hasn't started");
                    }

                    outputBuffer.position(bufferInfo.offset);
                    outputBuffer.limit(bufferInfo.offset + bufferInfo.size);

                    muxer.writeSampleData(videoTrackIndex, outputBuffer, bufferInfo);
                }

                encoder.releaseOutputBuffer(outputBufferIndex, false);

                if ((bufferInfo.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                    break;
                }
            }
        }
    }

    /**
     * 停止编码器
     */
    private void stopEncoder() {
        try {
            if (encoder != null) {
                encoder.stop();
                encoder.release();
                encoder = null;
            }

            if (inputSurface != null) {
                inputSurface.release();
                inputSurface = null;
            }

            if (muxer != null) {
                if (muxerStarted) {
                    muxer.stop();
                }
                muxer.release();
                muxer = null;
            }

            muxerStarted = false;
            videoTrackIndex = -1;

        } catch (Exception e) {
            Log.e(TAG, "Error stopping encoder", e);
        }
    }

    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "屏幕录制",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("屏幕录制服务通知");

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
    }

    /**
     * 创建通知
     */
    private Notification createNotification(String content) {
        Intent intent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        return new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("屏幕录制")
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build();
    }

    /**
     * 检查是否正在录制
     */
    public boolean isRecording() {
        return isRecording;
    }

    /**
     * 获取性能监控器
     */
    public PerformanceMonitor getPerformanceMonitor() {
        return performanceMonitor;
    }

    /**
     * 启动模拟模式
     */
    private void startSimulationMode() {
        Log.i(TAG, "Starting simulation mode for screen recording");

        // 创建模拟帧生成线程
        Thread simulationThread = new Thread(() -> {
            try {
                while (isRecording && !Thread.currentThread().isInterrupted()) {
                    // 创建模拟的屏幕截图
                    Bitmap simulatedFrame = createSimulatedFrame();
                    if (simulatedFrame != null && frameQueue.size() < 10) {
                        frameQueue.offer(simulatedFrame);
                    }

                    // 模拟30fps
                    Thread.sleep(33);
                }
            } catch (InterruptedException e) {
                Log.d(TAG, "Simulation thread interrupted");
            } catch (Exception e) {
                Log.e(TAG, "Error in simulation mode", e);
            }
        });

        simulationThread.start();
    }

    /**
     * 创建模拟帧
     */
    private Bitmap createSimulatedFrame() {
        try {
            // 创建一个简单的彩色渐变图像作为模拟帧
            Bitmap bitmap = Bitmap.createBitmap(screenWidth, screenHeight, Bitmap.Config.RGB_565);

            // 使用Canvas绘制模拟内容
            android.graphics.Canvas canvas = new android.graphics.Canvas(bitmap);

            // 背景色随时间变化
            long time = System.currentTimeMillis();
            int color = android.graphics.Color.HSVToColor(new float[]{
                (time / 100) % 360,  // 色相随时间变化
                0.5f,                // 饱和度
                0.8f                 // 亮度
            });

            canvas.drawColor(color);

            // 绘制一些文本信息
            android.graphics.Paint paint = new android.graphics.Paint();
            paint.setColor(android.graphics.Color.WHITE);
            paint.setTextSize(48);
            paint.setAntiAlias(true);

            String text = "模拟录制模式\n时间: " + new java.text.SimpleDateFormat("HH:mm:ss").format(new java.util.Date());
            String[] lines = text.split("\n");

            float y = screenHeight / 2f - (lines.length * 60) / 2f;
            for (String line : lines) {
                float x = (screenWidth - paint.measureText(line)) / 2f;
                canvas.drawText(line, x, y, paint);
                y += 60;
            }

            return bitmap;

        } catch (Exception e) {
            Log.e(TAG, "Error creating simulated frame", e);
            return null;
        }
    }
}
