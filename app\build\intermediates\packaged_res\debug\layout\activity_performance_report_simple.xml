<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#F5F5F5">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp"
        android:background="#FFFFFF"
        android:padding="12dp">

        <TextView
            android:id="@+id/title_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="📊 录屏性能分析报告"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#333333" />

        <Button
            android:id="@+id/close_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="关闭"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 摘要信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:background="#FFFFFF"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="📋 性能摘要"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="12dp" />

        <TextView
            android:id="@+id/summary_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="#666666"
            android:lineSpacingExtra="4dp" />

    </LinearLayout>

    <!-- 操作按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/save_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="保存报告"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/detail_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="查看详情"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- 详细报告区域 -->
    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#FFFFFF"
        android:padding="16dp">

        <TextView
            android:id="@+id/detail_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="#444444"
            android:lineSpacingExtra="2dp" />

    </ScrollView>

    <!-- 底部提示 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="💡 提示: 可以保存报告到文件或查看详细信息"
        android:textSize="12sp"
        android:textColor="#888888"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:background="#FFFFFF"
        android:padding="8dp" />

</LinearLayout>
