{"logs": [{"outputFile": "com.xiaopeng.xpautotest.app-mergeDebugResources-45:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\58c60d7f1f34ad6e3d961d9b76173355\\transformed\\core-1.13.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "49,50,51,52,53,54,55,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3771,3863,3964,4058,4152,4245,4339,9413", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3858,3959,4053,4147,4240,4334,4430,9509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9fb248392f055438974c3c6bf62bfc1\\transformed\\appcompat-1.7.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "871,966,1061,1161,1243,1340,1446,1523,1598,1689,1782,1879,1975,2069,2162,2257,2349,2440,2531,2609,2705,2800,2895,2992,3088,3186,3334,9116", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "961,1056,1156,1238,1335,1441,1518,1593,1684,1777,1874,1970,2064,2157,2252,2344,2435,2526,2604,2700,2795,2890,2987,3083,3181,3329,3423,9190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\93d9df77809f9ff1d7d7a500adecfe6e\\transformed\\material-1.12.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,911,973,1050,1109,1168,1246,1307,1364,1420,1479,1537,1591,1676,1732,1790,1844,1909,2001,2075,2147,2226,2300,2376,2498,2560,2622,2721,2800,2874,2924,2975,3041,3105,3174,3245,3316,3377,3448,3515,3575,3661,3740,3807,3890,3975,4049,4114,4190,4238,4311,4375,4451,4529,4591,4655,4718,4783,4863,4939,5017,5093,5147,5202,5271,5346,5419", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "242,306,368,438,508,585,676,782,855,906,968,1045,1104,1163,1241,1302,1359,1415,1474,1532,1586,1671,1727,1785,1839,1904,1996,2070,2142,2221,2295,2371,2493,2555,2617,2716,2795,2869,2919,2970,3036,3100,3169,3240,3311,3372,3443,3510,3570,3656,3735,3802,3885,3970,4044,4109,4185,4233,4306,4370,4446,4524,4586,4650,4713,4778,4858,4934,5012,5088,5142,5197,5266,5341,5414,5484"}, "to": {"startLines": "13,44,45,46,47,48,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "724,3428,3492,3554,3624,3694,4435,4526,4632,4705,4756,4818,4895,4954,5013,5091,5152,5209,5265,5324,5382,5436,5521,5577,5635,5689,5754,5846,5920,5992,6071,6145,6221,6343,6405,6467,6566,6645,6719,6769,6820,6886,6950,7019,7090,7161,7222,7293,7360,7420,7506,7585,7652,7735,7820,7894,7959,8035,8083,8156,8220,8296,8374,8436,8500,8563,8628,8708,8784,8862,8938,8992,9047,9195,9270,9343", "endLines": "16,44,45,46,47,48,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,124,125,126", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "866,3487,3549,3619,3689,3766,4521,4627,4700,4751,4813,4890,4949,5008,5086,5147,5204,5260,5319,5377,5431,5516,5572,5630,5684,5749,5841,5915,5987,6066,6140,6216,6338,6400,6462,6561,6640,6714,6764,6815,6881,6945,7014,7085,7156,7217,7288,7355,7415,7501,7580,7647,7730,7815,7889,7954,8030,8078,8151,8215,8291,8369,8431,8495,8558,8623,8703,8779,8857,8933,8987,9042,9111,9265,9338,9408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fc66c71e6d2f7749ba39454a10f3998a\\transformed\\jetified-lib_xpui-5.6.3\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,31,44,50,59,64,69,74,79,88,99,105,111,122,128,134,145,151,157,166,177,183,189,195,203,214,220,226,232,240,251,257,263,269,277", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,106,157,208,259,313,367,420,473,525,577,629,674,718,775,827,880,940,991,1041,1096,1153,1205,1253,1514,2284,2590,3127,3332,3634,3928,4230,4714,5473,5863,6261,7006,7398,7798,8563,8959,9363,9823,10581,10965,11329,11672,12206,12950,13336,13702,14047,14583,15347,15737,16107,16456,16996", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,30,43,49,58,63,68,73,78,87,98,104,110,121,127,133,144,150,156,165,176,182,188,194,202,213,219,225,231,239,250,256,262,268,276,280", "endColumns": "50,50,50,50,53,53,52,52,51,51,51,44,43,56,51,52,59,50,49,54,56,51,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "101,152,203,254,308,362,415,468,520,572,624,669,713,770,822,875,935,986,1036,1091,1148,1200,1248,1509,2279,2585,3122,3327,3629,3923,4225,4709,5468,5858,6256,7001,7393,7793,8558,8954,9358,9818,10576,10960,11324,11667,12201,12945,13331,13697,14042,14578,15342,15732,16102,16451,16991,17157"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,128,129,130,131,132,133,134,135,136,137,138,139,140,146,159,165,174,179,184,189,194,203,214,220,226,237,243,249,260,266,272,281,292,298,304,310,318,329,335,341,347,355,366,372,378,384,392", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,201,252,303,354,408,462,515,568,620,672,9514,9559,9603,9660,9712,9765,9825,9876,9926,9981,10038,10090,10138,10385,11155,11461,11906,12111,12413,12707,13009,13493,14252,14642,15040,15785,16177,16577,17342,17738,18142,18602,19360,19744,20108,20451,20985,21729,22115,22481,22826,23362,24126,24516,24886,25235,25775", "endLines": "2,3,4,5,6,7,8,9,10,11,12,128,129,130,131,132,133,134,135,136,137,138,139,145,158,164,173,178,183,188,193,202,213,219,225,236,242,248,259,265,271,280,291,297,303,309,317,328,334,340,346,354,365,371,377,383,391,395", "endColumns": "50,50,50,50,53,53,52,52,51,51,51,44,43,56,51,52,59,50,49,54,56,51,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "196,247,298,349,403,457,510,563,615,667,719,9554,9598,9655,9707,9760,9820,9871,9921,9976,10033,10085,10133,10380,11150,11456,11901,12106,12408,12702,13004,13488,14247,14637,15035,15780,16172,16572,17337,17733,18137,18597,19355,19739,20103,20446,20980,21724,22110,22476,22821,23357,24121,24511,24881,25230,25770,25936"}}]}]}