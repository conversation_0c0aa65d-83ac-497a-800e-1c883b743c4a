package com.xiaopeng.executor.action.variable;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.executor.bean.VariableContext;
import com.xiaopeng.executor.core.VariableResolver;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.screen.ScreenManager;

/**
 * 变量设置动作
 *
 * 实现Set关键字的变量定义功能：
 * 语法：Set VARIABLE_NAME "value" 或 Set VARIABLE_NAME 123
 *
 * 功能：
 * 1. 解析变量名和变量值
 * 2. 验证变量名的有效性
 * 3. 将变量存储到VariableContext
 * 4. 支持字符串、数值、布尔值类型
 */
public class SetVariableAction extends BaseAction {

    private static final String TAG = "SetVariableAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        VariableContext variableContext = getVariableContext(context);

        try {
            // 获取并验证变量名
            String variableName = getRequiredStringParam(context, "Variable name");

            // 验证变量名格式
            if (!VariableResolver.isValidVariableName(variableName)) {
                throw new ActionException("Invalid variable name: " + variableName, FailureCode.SI001);
            }

            // 获取变量值（允许空值）
            String variableValue = context.getStringParam();
            if (variableValue == null) {
                variableValue = "";
            }

            // 处理字符串值
            variableValue = processStringValue(variableValue);

            // 设置变量
            variableContext.setValue(variableName, variableValue);

            // 特殊处理：如果是TARGET_SCERRN变量，同时设置屏幕切换
            if ("TARGET_SCREEN".equals(variableName)) {
                return handleScreenNameVariable(variableValue);
            }

            String message = "Variable set: " + variableName + " = " + variableValue;
            FileLogger.i(TAG, message);

            return TestResult.success(message);
            
        } catch (Exception e) {
            String errorMsg = "Failed to set variable: " + e.getMessage();
            FileLogger.e(TAG, errorMsg, e);
            throw new ActionException(errorMsg, FailureCode.AB001, e);
        }
    }

    /**
     * 处理TARGET_SCREEN变量设置
     *
     * 当设置TARGET_SCREEN变量时，除了存储变量值外，还会执行屏幕切换操作。
     * 这样可以确保变量值与实际屏幕状态保持一致。
     *
     * @param variableValue 变量值（屏幕名称，如ivi、rse、psg或者0、2、3等）
     * @return 测试结果，包含屏幕切换的成功或失败信息
     */
    private TestResult handleScreenNameVariable(String variableValue) {
        try {
            ScreenManager screenManager = ScreenManager.getInstance();
            ScreenManager.ScreenSwitchResult result = screenManager.setScreen(variableValue);
            String msg = result.message;
            FileLogger.i(TAG, msg);

            if (result.success) {
                return TestResult.success(msg);
            } else {
                return TestResult.failure(msg);
            }
        } catch (Exception e) {
            String errorMessage = "Failed to switch screen for variable " + variableValue + ": " + e.getMessage();
            FileLogger.e(TAG, errorMessage, e);
            return TestResult.failure(errorMessage);
        }
    }
}
