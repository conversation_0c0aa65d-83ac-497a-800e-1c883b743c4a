package com.xiaopeng.xpautotest.utils;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * minicap二进制文件管理器
 * 负责minicap和minicap.so文件的部署、权限设置和进程管理
 */
public class MinicapManager {
    private static final String TAG = "MinicapManager";
    
    // minicap文件路径
    private static final String MINICAP_BIN = "minicap";
    private static final String MINICAP_SO = "minicap.so";
    
    private Context context;
    private String minicapPath;
    private String minicapSoPath;
    private String deployDir;
    private Process minicapProcess;

    public MinicapManager(Context context) {
        this.context = context;
        // 使用/sdcard/AutoTest/minicap/目录
        this.deployDir = "/sdcard/AutoTest/minicap/";
        this.minicapPath = deployDir + MINICAP_BIN;
        this.minicapSoPath = deployDir + MINICAP_SO;

        // 确保目录存在
        createDeployDirectory();
    }

    /**
     * 创建部署目录
     */
    private void createDeployDirectory() {
        try {
            File dir = new File(deployDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                Log.d(TAG, "Created deploy directory: " + deployDir + ", success: " + created);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error creating deploy directory", e);
        }
    }
    
    /**
     * 部署minicap文件到设备
     * @return 是否部署成功
     */
    public boolean deployMinicap() {
        try {
            // 获取设备ABI
            String abi = getDeviceAbi();
            Log.d(TAG, "Device ABI: " + abi);

            boolean minicapDeployed = false;
            boolean minicapSoDeployed = false;

            // 部署minicap二进制文件
            try {
                minicapDeployed = deployAssetFile("minicap/" + abi + "/" + MINICAP_BIN, minicapPath);
                if (!minicapDeployed) {
                    Log.w(TAG, "Failed to deploy minicap binary, will try alternatives");
                }
            } catch (Exception e) {
                Log.w(TAG, "Exception deploying minicap binary", e);
            }

            // 部署minicap.so文件
            try {
                minicapSoDeployed = deployAssetFile("minicap/" + abi + "/" + MINICAP_SO, minicapSoPath);
                if (!minicapSoDeployed) {
                    Log.w(TAG, "Failed to deploy minicap.so, will try alternatives");
                }
            } catch (Exception e) {
                Log.w(TAG, "Exception deploying minicap.so", e);
            }

            // 设置可执行权限
            if (minicapDeployed) {
                try {
                    setExecutablePermission(minicapPath);
                } catch (Exception e) {
                    Log.w(TAG, "Failed to set executable permission", e);
                }
            }

            boolean success = minicapDeployed && minicapSoDeployed;
            if (success) {
                Log.i(TAG, "Minicap deployed successfully");
            } else {
                Log.w(TAG, "Minicap deployment incomplete, will try system alternatives");
            }

            return success;

        } catch (Exception e) {
            Log.e(TAG, "Error deploying minicap", e);
            return false;
        }
    }
    
    /**
     * 启动minicap进程
     * @param width 屏幕宽度
     * @param height 屏幕高度
     * @param socketName 本地套接字名称
     * @return 是否启动成功
     */
    public boolean startMinicap(int width, int height, String socketName) {
        try {
            if (minicapProcess != null && minicapProcess.isAlive()) {
                Log.w(TAG, "Minicap process is already running");
                return true;
            }

            // 启动minicap
            return startMinicapDirect(width, height);

        } catch (Exception e) {
            Log.e(TAG, "Error starting minicap process", e);
            return false;
        }
    }



    /**
     * 直接启动minicap
     */
    private boolean startMinicapDirect(int width, int height) {
        try {
            // 构建minicap命令
            String command = String.format(
                "cd %s && LD_LIBRARY_PATH=. ./%s -P %dx%d@%dx%d/0 -S",
                deployDir, MINICAP_BIN, width, height, width, height
            );

            Log.d(TAG, "Starting minicap directly: " + command);

            minicapProcess = Runtime.getRuntime().exec(new String[]{"sh", "-c", command});

            // 等待一小段时间确保进程启动
            Thread.sleep(2000);

            if (minicapProcess.isAlive()) {
                Log.i(TAG, "Minicap started successfully directly");
                return true;
            } else {
                Log.e(TAG, "Minicap process failed to start directly");
                return false;
            }

        } catch (Exception e) {
            Log.w(TAG, "Failed to start minicap directly", e);
            return false;
        }
    }


    
    /**
     * 停止minicap进程
     */
    public void stopMinicap() {
        if (minicapProcess != null) {
            try {
                minicapProcess.destroy();
                minicapProcess.waitFor();
                Log.i(TAG, "Minicap process stopped");
            } catch (InterruptedException e) {
                Log.e(TAG, "Error stopping minicap process", e);
                minicapProcess.destroyForcibly();
            } finally {
                minicapProcess = null;
            }
        }
    }
    
    /**
     * 检查minicap是否正在运行
     */
    public boolean isMinicapRunning() {
        return minicapProcess != null && minicapProcess.isAlive();
    }
    
    /**
     * 获取设备ABI
     */
    private String getDeviceAbi() {
        String abi = Build.SUPPORTED_ABIS[0];
        // 映射到minicap支持的ABI
        switch (abi) {
            case "arm64-v8a":
                return "arm64-v8a";
            case "armeabi-v7a":
                return "armeabi-v7a";
            case "x86":
                return "x86";
            case "x86_64":
                return "x86_64";
            default:
                Log.w(TAG, "Unsupported ABI: " + abi + ", fallback to arm64-v8a");
                return "arm64-v8a";
        }
    }
    
    /**
     * 从assets部署文件到设备
     */
    private boolean deployAssetFile(String assetPath, String targetPath) {
        try {
            // 检查assets文件是否存在
            if (!assetFileExists(assetPath)) {
                Log.w(TAG, "Asset file does not exist: " + assetPath);
                return false;
            }

            // 直接复制到目标位置
            File targetFile = new File(targetPath);
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            try (InputStream inputStream = context.getAssets().open(assetPath);
                 OutputStream outputStream = new FileOutputStream(targetFile)) {

                byte[] buffer = new byte[8192];
                int length;
                while ((length = inputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, length);
                }
                outputStream.flush();
            }

            // 设置可执行权限
            if (targetPath.endsWith("minicap")) {
                targetFile.setExecutable(true);
            }

            Log.d(TAG, "Deployed " + assetPath + " to " + targetPath + ", size: " + targetFile.length());
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error deploying asset file: " + assetPath, e);
            return false;
        }
    }


    
    /**
     * 设置文件可执行权限
     */
    private boolean setExecutablePermission(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                boolean result = file.setExecutable(true, false);
                Log.d(TAG, "Set executable permission for " + filePath + ": " + result);
                return result;
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error setting executable permission", e);
            return false;
        }
    }
    
    /**
     * 检查assets文件是否存在
     */
    private boolean assetFileExists(String assetPath) {
        try {
            InputStream inputStream = context.getAssets().open(assetPath);
            inputStream.close();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 清理部署的文件
     */
    public void cleanup() {
        stopMinicap();

        try {
            File minicapFile = new File(minicapPath);
            File minicapSoFile = new File(minicapSoPath);

            if (minicapFile.exists()) {
                minicapFile.delete();
                Log.d(TAG, "Cleaned up minicap binary");
            }

            if (minicapSoFile.exists()) {
                minicapSoFile.delete();
                Log.d(TAG, "Cleaned up minicap.so");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup", e);
        }
    }
}
