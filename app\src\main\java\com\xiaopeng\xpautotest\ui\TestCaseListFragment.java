package com.xiaopeng.xpautotest.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.adapter.TestCaseAdapter;
import com.xiaopeng.xpautotest.helper.DialogHelper;
import com.xiaopeng.xpautotest.manager.TestManager;
import com.xiaopeng.xpautotest.model.ITestDataState;
import com.xiaopeng.xpautotest.model.ITestSuiteItem;
import com.xiaopeng.xpautotest.model.TestCaseItem;
import com.xiaopeng.xpautotest.viewmodel.SuiteViewModel;
import com.xiaopeng.xui.app.XDialogInterface;
import com.xiaopeng.xui.app.XToast;
import com.xiaopeng.xui.widget.XButton;
import com.xiaopeng.xui.widget.XTextView;
import java.util.ArrayList;
import java.util.List;
import android.text.SpannableString;
import android.text.Spannable;
import android.text.style.ForegroundColorSpan;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class TestCaseListFragment extends Fragment {
    private static final String TAG = "TestCaseListFragment";
    private SuiteViewModel viewModel;
    private TestCaseAdapter adapter;

    private XButton mExecuteAllButton;
    private XButton mRetryFailedButton;
    private XTextView mDirectoryNameTextView;
    private XTextView mStatisticsTextView;

    // 缓存失败用例数据
    private List<TestCaseItem> cachedFailedCases = new ArrayList<>();

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.i(TAG, "onCreateView(" + getClass().getSimpleName() + "):" + this.hashCode());
        return inflater.inflate(R.layout.at_fragment_factory_sub_directory, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);

        // 初始化RecyclerView
        RecyclerView rvScripts = view.findViewById(R.id.rv_test_case_list);
        rvScripts.setLayoutManager(new LinearLayoutManager(requireContext()));
        adapter = new TestCaseAdapter(new ArrayList<>(), new TestCaseAdapter.OnActionClickListener() {
            @Override
            public void onExecuteClick(TestCaseItem caseItem) {
                // 单个执行测试用例按钮点击事件，从task中获取已有的执行ID
//                viewModel.executeCase(caseItem);
                ((MainActivity) getContext()).startCaseTesting(caseItem, caseItem.getTaskExecutionId());
//                viewModel.startTest(new TestManager.ITestDataCallBack<ITestDataState<Long>>() {
//                    @Override
//                    public void onUpdate(ITestDataState<Long> state) {
//                        if (state instanceof ITestDataState.Success) {
//                            Long executionId = ((ITestDataState.Success<Long>) state).getData();
//                            ((MainActivity) getContext()).startCaseTesting(caseItem, executionId);
//                            Log.i(TAG, "start case test successful: " + executionId);
//                        } else if (state instanceof ITestDataState.Error) {
//                            Throwable error = ((ITestDataState.Error<Long>) state).getError();
//                            Log.e(TAG, "start case test error: " + error.getMessage());
//                        }
//                    }
//                });
            }

            @Override
            public void onStopClick(TestCaseItem caseItem) {
                // 单个停止测试用例按钮点击事件
//                viewModel.stopCase(caseItem);
                ((MainActivity) getContext()).stopTesting();
            }
        });

        rvScripts.setAdapter(adapter);

        Log.i("TestCaseListFragment", "onViewCreated: ");

        viewModel.getSelectedSuite().observe(getViewLifecycleOwner(), suite -> {
            if (suite != null) {
                mDirectoryNameTextView.setText(suite.getName());
                adapter.updateData(suite.getItems());
                updateStatistics(suite.getItems());
            }
        });

        // 观察数据变化
//        viewModel.getCurrentCases().observe(getViewLifecycleOwner(), scripts -> {
//            adapter.updateData(scripts);
//        });

        viewModel.getExecutingCase().observe(getViewLifecycleOwner(), script -> {
            if (script != null) {
                int position = adapter.getTestCases().indexOf(script);
                if (position != -1) {
                    adapter.notifyItemChanged(position);
                }
            }
        });

        viewModel.getLatestResult().observe(getViewLifecycleOwner(), result -> {
            adapter.updateTestResult(result);
            // 更新统计信息和重跑按钮可见性
            if (viewModel.getSelectedSuite().getValue() != null) {
                updateStatistics(viewModel.getSelectedSuite().getValue().getItems());
            }
        });
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        viewModel = new ViewModelProvider(requireActivity()).get(SuiteViewModel.class);
    }

    private void initViews(View parentView) {
        this.mExecuteAllButton = parentView.findViewById(R.id.btn_execute_all);
        this.mExecuteAllButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                if (getComponentClient().isExecuting()) {
//                    XToast.show(getString(R.string.tips_function_executing));
//                    return;
//                }
//
//                List<Long> componentIdList = getDirectoryClient().getComponentIdsRecursive(directoryId);
//                if (CheckUtils.isEmpty(componentIdList)) {
//                    XToast.show(getString(R.string.tips_function_no_executable));
//                    return;
//                }

                String title = getContext().getString(R.string.retry_execute_confirm_title);
                String messageFormat = getContext().getString(R.string.retry_execute_confirm_message);
                String message = String.format(messageFormat, mDirectoryNameTextView.getText());
                String positiveButtonText = getContext().getString(R.string.retry_execute_confirm_positive_button);
                String negativeButtonText = getContext().getString(R.string.retry_execute_confirm_negative_button);

                DialogHelper.getInstance().showDialog(getContext(), title, message,
                        positiveButtonText, negativeButtonText, (xDialog, buttonId) -> {
                            xDialog.dismiss();

//                            EventHelper.executeComponentConfirmation(directoryId, 0, buttonId);

                            switch (buttonId) {
                                case XDialogInterface.BUTTON_POSITIVE:
                                    ITestSuiteItem suite = viewModel.getSelectedSuite().getValue();
                                    if (suite == null) {
                                        Log.e(TAG, "No test suite selected");
                                        XToast.show("No test suite selected");
                                        return;
                                    }
                                    viewModel.startTest(new TestManager.ITestDataCallBack<ITestDataState<Long>>() {
                                        @Override
                                        public void onUpdate(ITestDataState<Long> state) {
                                            if (state instanceof ITestDataState.Success) {
                                                Long executionId = ((ITestDataState.Success<Long>) state).getData();
                                                ((MainActivity) getContext()).startSuiteTesting(suite, executionId);
                                                Log.i(TAG, "start suite test successful: " + executionId);
                                            } else if (state instanceof ITestDataState.Error) {
                                                Throwable error = ((ITestDataState.Error<Long>) state).getError();
                                                Log.e(TAG, "start suite test error: " + error.getMessage());
                                            }
                                        }
                                    });
//                                    if (stationGroup != null) {
//                                        // save station group execute state
//                                        saveStationGroupRecord(directoryId, stationGroup);
//                                    }
//
//                                    getComponentClient().executeBatchAsync(
//                                            directoryId, componentIdList, new ComponentExecuteListener() {
//                                                @Override
//                                                public void beforeExecution(long componentId) {
//                                                    ForwardHelper.refreshFactoryTabStatus();
//                                                    ForwardHelper.refreshComponentExecuteState(componentId, FactorySubDirectoryFragment.class);
//                                                }
//
//                                                @Override
//                                                public void afterExecution(long componentId, ComponentExecuteResult result) {
//                                                    ForwardHelper.refreshFactoryTabStatus();
//                                                    ForwardHelper.refreshComponentExecuteState(componentId, FactorySubDirectoryFragment.class);
//                                                }
//                                            });

                                    break;

                                case XDialogInterface.BUTTON_NEGATIVE:
//                                    EventHelper.executeComponentCancel(directoryId, 0, "cancel");
                                    break;
                            }
                        });
            }
        });

        this.mDirectoryNameTextView = parentView.findViewById(R.id.tv_ota_version);
        this.mStatisticsTextView = parentView.findViewById(R.id.tv_statistics);
        this.mRetryFailedButton = parentView.findViewById(R.id.btn_retry_failed);

        // 设置重跑按钮点击事件
        this.mRetryFailedButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                retryFailedCases();
            }
        });
    }

    private void updateStatistics(List<TestCaseItem> testCases) {
        if (testCases == null || mStatisticsTextView == null) {
            return;
        }

        int totalCount = testCases.size();
        int successCount = 0;
        int failureCount = 0;

        // 清空并重新收集失败用例
        cachedFailedCases.clear();

        for (TestCaseItem testCase : testCases) {
            if (testCase.isSuccess()) {
                successCount++;
            } else if (testCase.isFailure()) {
                failureCount++;
                // 缓存失败用例，避免重跑时重复查询
                cachedFailedCases.add(testCase);
            }
        }

        // 创建带颜色的文本：失败数（红色）/成功数（绿色）/总数（普通颜色）
        String statisticsText = String.format("%d/%d/%d", failureCount, successCount, totalCount);

        // 设置不同部分的颜色
        SpannableString spannableString = new SpannableString(statisticsText);
        // 设置失败数为红色（与状态图标一致）
        String failureStr = String.valueOf(failureCount);
        int failureStart = 0;
        int failureEnd = failureStr.length();
        spannableString.setSpan(new ForegroundColorSpan(
                getResources().getColor(R.color.bg_status_execute_failure)),
                failureStart, failureEnd, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        // 设置成功数为绿色（与状态图标一致）
        String successStr = String.valueOf(successCount);
        int successStart = failureStr.length() + 1; // +1 for the "/"
        int successEnd = successStart + successStr.length();
        spannableString.setSpan(new ForegroundColorSpan(
                getResources().getColor(R.color.bg_status_execute_success)),
                successStart, successEnd, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        mStatisticsTextView.setText(spannableString);

        // 控制重跑按钮的可见性：仅在失败用例数大于0时显示
        if (mRetryFailedButton != null) {
            mRetryFailedButton.setVisibility(failureCount > 0 ? View.VISIBLE : View.GONE);
        }
    }

    private void retryFailedCases() {
        ITestSuiteItem suite = viewModel.getSelectedSuite().getValue();
        if (suite == null) {
            Log.e(TAG, "No test suite selected for retry");
            return;
        }

        // 使用缓存的失败用例数据
        if (cachedFailedCases.isEmpty()) {
            Log.w(TAG, "No failed cases to retry");
            return;
        }
        // 获取当前的executionId，使用失败用例中的taskExecutionId
        long executionId = cachedFailedCases.get(0).getTaskExecutionId();
        // 直接启动失败用例的重跑
        ((MainActivity) getContext()).startFailedCaseTesting(cachedFailedCases, executionId, suite.getId());
        Log.i(TAG, "start failed cases retry successful: " + executionId + ", failed cases count: " + cachedFailedCases.size());
    }
}
