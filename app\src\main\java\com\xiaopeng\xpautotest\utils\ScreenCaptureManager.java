package com.xiaopeng.xpautotest.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.PixelFormat;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.Image;
import android.media.ImageReader;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.WindowManager;

import java.nio.ByteBuffer;

/**
 * 屏幕捕获管理器
 * 使用Android MediaProjection API作为minicap的替代方案
 */
public class ScreenCaptureManager {
    private static final String TAG = "ScreenCaptureManager";
    
    private Context context;
    private MediaProjectionManager projectionManager;
    private MediaProjection mediaProjection;
    private VirtualDisplay virtualDisplay;
    private ImageReader imageReader;
    private HandlerThread backgroundThread;
    private Handler backgroundHandler;
    
    private int screenWidth;
    private int screenHeight;
    private int screenDensity;
    
    private FrameCallback frameCallback;
    private boolean isCapturing = false;
    
    public interface FrameCallback {
        void onFrameReceived(Bitmap frame);
        void onError(String error);
    }
    
    public ScreenCaptureManager(Context context) {
        this.context = context;
        this.projectionManager = (MediaProjectionManager) context.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        
        initScreenParameters();
        initBackgroundThread();
    }
    
    /**
     * 初始化屏幕参数
     */
    private void initScreenParameters() {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        if (windowManager != null) {
            Display display = windowManager.getDefaultDisplay();
            DisplayMetrics metrics = new DisplayMetrics();
            display.getRealMetrics(metrics);
            
            screenWidth = metrics.widthPixels;
            screenHeight = metrics.heightPixels;
            screenDensity = metrics.densityDpi;
            
            Log.d(TAG, String.format("Screen parameters: %dx%d, density: %d", 
                screenWidth, screenHeight, screenDensity));
        }
    }
    
    /**
     * 初始化后台线程
     */
    private void initBackgroundThread() {
        backgroundThread = new HandlerThread("ScreenCapture");
        backgroundThread.start();
        backgroundHandler = new Handler(backgroundThread.getLooper());
    }
    
    /**
     * 设置帧回调
     */
    public void setFrameCallback(FrameCallback callback) {
        this.frameCallback = callback;
    }
    
    /**
     * 开始屏幕捕获
     * 注意：这个方法需要用户授权，通常用于测试
     */
    public boolean startCapture() {
        try {
            if (isCapturing) {
                Log.w(TAG, "Already capturing");
                return true;
            }
            
            // 创建ImageReader
            imageReader = ImageReader.newInstance(screenWidth, screenHeight, PixelFormat.RGBA_8888, 2);
            imageReader.setOnImageAvailableListener(new ImageReader.OnImageAvailableListener() {
                @Override
                public void onImageAvailable(ImageReader reader) {
                    processImage(reader);
                }
            }, backgroundHandler);
            
            // 注意：在实际应用中，需要通过startActivityForResult获取MediaProjection
            // 这里我们尝试使用系统级权限直接创建
            if (createMediaProjection()) {
                createVirtualDisplay();
                isCapturing = true;
                Log.i(TAG, "Screen capture started successfully");
                return true;
            } else {
                Log.e(TAG, "Failed to create MediaProjection");
                return false;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting screen capture", e);
            return false;
        }
    }
    
    /**
     * 创建MediaProjection（系统级应用可能可以直接创建）
     */
    private boolean createMediaProjection() {
        try {
            // 对于系统级应用，尝试直接创建MediaProjection
            // 注意：这可能需要特殊权限或在某些设备上不工作
            
            // 这里返回false，表示需要用户授权
            // 在实际使用中，应该通过正常的授权流程
            Log.w(TAG, "MediaProjection requires user authorization");
            return false;
            
        } catch (Exception e) {
            Log.e(TAG, "Error creating MediaProjection", e);
            return false;
        }
    }
    
    /**
     * 创建虚拟显示
     */
    private void createVirtualDisplay() {
        if (mediaProjection != null && imageReader != null) {
            virtualDisplay = mediaProjection.createVirtualDisplay(
                "ScreenCapture",
                screenWidth, screenHeight, screenDensity,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader.getSurface(),
                null, backgroundHandler
            );
        }
    }
    
    /**
     * 处理捕获的图像
     */
    private void processImage(ImageReader reader) {
        Image image = null;
        try {
            image = reader.acquireLatestImage();
            if (image != null) {
                Bitmap bitmap = imageToBitmap(image);
                if (bitmap != null && frameCallback != null) {
                    frameCallback.onFrameReceived(bitmap);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error processing image", e);
            if (frameCallback != null) {
                frameCallback.onError("Error processing image: " + e.getMessage());
            }
        } finally {
            if (image != null) {
                image.close();
            }
        }
    }
    
    /**
     * 将Image转换为Bitmap
     */
    private Bitmap imageToBitmap(Image image) {
        try {
            Image.Plane[] planes = image.getPlanes();
            ByteBuffer buffer = planes[0].getBuffer();
            int pixelStride = planes[0].getPixelStride();
            int rowStride = planes[0].getRowStride();
            int rowPadding = rowStride - pixelStride * screenWidth;
            
            Bitmap bitmap = Bitmap.createBitmap(
                screenWidth + rowPadding / pixelStride, 
                screenHeight, 
                Bitmap.Config.ARGB_8888
            );
            bitmap.copyPixelsFromBuffer(buffer);
            
            // 如果有padding，需要裁剪
            if (rowPadding != 0) {
                bitmap = Bitmap.createBitmap(bitmap, 0, 0, screenWidth, screenHeight);
            }
            
            return bitmap;
            
        } catch (Exception e) {
            Log.e(TAG, "Error converting image to bitmap", e);
            return null;
        }
    }
    
    /**
     * 停止屏幕捕获
     */
    public void stopCapture() {
        isCapturing = false;
        
        try {
            if (virtualDisplay != null) {
                virtualDisplay.release();
                virtualDisplay = null;
            }
            
            if (mediaProjection != null) {
                mediaProjection.stop();
                mediaProjection = null;
            }
            
            if (imageReader != null) {
                imageReader.close();
                imageReader = null;
            }
            
            Log.i(TAG, "Screen capture stopped");
            
        } catch (Exception e) {
            Log.e(TAG, "Error stopping screen capture", e);
        }
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        stopCapture();
        
        if (backgroundThread != null) {
            backgroundThread.quitSafely();
            try {
                backgroundThread.join();
            } catch (InterruptedException e) {
                Log.e(TAG, "Error joining background thread", e);
            }
            backgroundThread = null;
            backgroundHandler = null;
        }
    }
    
    /**
     * 检查是否正在捕获
     */
    public boolean isCapturing() {
        return isCapturing;
    }
    
    /**
     * 获取屏幕尺寸
     */
    public int getScreenWidth() {
        return screenWidth;
    }
    
    public int getScreenHeight() {
        return screenHeight;
    }
    
    /**
     * 设置MediaProjection（从授权Activity获取）
     */
    public void setMediaProjection(MediaProjection projection) {
        this.mediaProjection = projection;
    }
}
