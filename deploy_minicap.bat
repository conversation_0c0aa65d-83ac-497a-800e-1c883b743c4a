@echo off
setlocal enabledelayedexpansion

echo === Minicap部署脚本 ===

REM 检查adb命令
where adb >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到adb命令，请确保Android SDK已安装并添加到PATH
    pause
    exit /b 1
)

REM 检查设备连接
for /f "tokens=*" %%i in ('adb devices ^| find /c "device"') do set DEVICE_COUNT=%%i
set /a DEVICE_COUNT=%DEVICE_COUNT%-1
if %DEVICE_COUNT% leq 0 (
    echo 错误: 未找到连接的Android设备
    echo 请确保设备已连接并启用USB调试
    pause
    exit /b 1
)

echo 找到 %DEVICE_COUNT% 个设备

REM 获取设备架构
for /f "tokens=*" %%i in ('adb shell getprop ro.product.cpu.abi') do set ABI=%%i
echo 设备架构: %ABI%

REM 设置minicap文件路径
set MINICAP_DIR=app\src\main\assets\minicap\%ABI%
set MINICAP_BIN=%MINICAP_DIR%\minicap
set MINICAP_SO=%MINICAP_DIR%\minicap.so

REM 检查minicap文件是否存在
if not exist "%MINICAP_BIN%" (
    echo 错误: 未找到minicap文件: %MINICAP_BIN%
    echo 请先从STF项目下载对应架构的minicap文件
    pause
    exit /b 1
)

if not exist "%MINICAP_SO%" (
    echo 错误: 未找到minicap.so文件: %MINICAP_SO%
    echo 请先从STF项目下载对应架构的minicap.so文件
    pause
    exit /b 1
)

echo 找到minicap文件:
echo   - %MINICAP_BIN%
echo   - %MINICAP_SO%

REM 创建目标目录
echo 创建目标目录...
adb shell "mkdir -p /data/local/tmp"

REM 推送文件
echo 推送minicap文件...
adb push "%MINICAP_BIN%" /data/local/tmp/minicap
if %errorlevel% neq 0 (
    echo 错误: 推送minicap失败
    pause
    exit /b 1
)

adb push "%MINICAP_SO%" /data/local/tmp/minicap.so
if %errorlevel% neq 0 (
    echo 错误: 推送minicap.so失败
    pause
    exit /b 1
)

REM 设置权限
echo 设置文件权限...
adb shell "chmod 755 /data/local/tmp/minicap"
adb shell "chmod 644 /data/local/tmp/minicap.so"

REM 验证部署
echo 验证部署结果...
adb shell "ls -la /data/local/tmp/minicap*"

REM 测试minicap
echo 测试minicap...
for /f "tokens=2 delims=:" %%i in ('adb shell wm size ^| find "Physical size"') do set SCREEN_SIZE=%%i
set SCREEN_SIZE=%SCREEN_SIZE: =%
if "%SCREEN_SIZE%"=="" set SCREEN_SIZE=1920x1080

echo 屏幕尺寸: %SCREEN_SIZE%

REM 启动minicap测试
echo 启动minicap测试（5秒）...
start /b adb shell "cd /data/local/tmp && timeout 5 LD_LIBRARY_PATH=. ./minicap -P %SCREEN_SIZE%@%SCREEN_SIZE%/0 -S"

timeout /t 2 /nobreak >nul

REM 检查minicap是否运行
adb shell "ps | grep minicap" >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ minicap启动成功！
) else (
    echo ❌ minicap启动失败
    echo 请检查设备权限和文件完整性
)

REM 停止测试
adb shell "pkill minicap" >nul 2>nul

echo.
echo === 部署完成 ===
echo 现在可以在应用中使用屏幕录制功能了
echo.
echo 如果遇到问题，请检查:
echo 1. 设备是否有root权限或系统级应用权限
echo 2. minicap文件是否与设备架构匹配
echo 3. /data/local/tmp目录是否可写
echo.
echo 手动测试命令:
echo adb shell
echo cd /data/local/tmp
echo LD_LIBRARY_PATH=. ./minicap -P %SCREEN_SIZE%@%SCREEN_SIZE%/0 -S

pause
