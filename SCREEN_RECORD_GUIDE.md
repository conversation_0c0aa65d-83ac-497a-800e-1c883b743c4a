# 屏幕录制功能集成指南

## 概述

本文档详细说明了如何在原生Android应用中集成基于STF minicap的屏幕录制功能，并实现实时性能监控。

## 功能特性

- 🎥 基于STF minicap的高性能屏幕捕捉
- 📊 实时CPU和内存使用率监控
- 🎬 H.264视频编码和MP4文件输出
- 📱 前台服务确保录制稳定性
- 🔧 完整的权限管理和错误处理

## 系统要求

- Android API 28+ (Android 9.0+)
- 支持的ABI: arm64-v8a, armeabi-v7a, x86, x86_64
- 系统级权限 (android.uid.system)

## 核心组件

### 1. MinicapManager
负责minicap二进制文件的部署和进程管理。

**主要功能:**
- 自动检测设备ABI并部署对应的minicap文件
- 管理minicap进程的启动和停止
- 处理文件权限设置

### 2. MinicapDataCapture
处理minicap数据流的捕获和解析。

**主要功能:**
- 连接minicap本地套接字
- 解析banner信息获取屏幕参数
- 实时捕获和解码图像帧

### 3. PerformanceMonitor
实时监控应用性能指标。

**监控指标:**
- CPU使用率 (%)
- PSS内存占用 (KB)
- 私有脏内存 (KB)
- 共享脏内存 (KB)

### 4. ScreenRecordService
核心录制服务，集成所有功能模块。

**服务特性:**
- 前台服务保证录制稳定性
- MediaCodec H.264编码
- MediaMuxer MP4文件封装
- 自动文件管理和存储

## minicap配置指南

### 1. 获取minicap二进制文件

从STF项目获取预编译的minicap文件：
```bash
# 下载STF minicap
git clone https://github.com/openstf/minicap.git
cd minicap

# 编译或下载预编译版本
# 需要获取以下文件：
# - minicap (主程序)
# - minicap.so (共享库)
```

### 2. 文件结构

在项目的 `app/src/main/assets/` 目录下创建以下结构：
```
assets/
└── minicap/
    ├── arm64-v8a/
    │   ├── minicap
    │   └── minicap.so
    ├── armeabi-v7a/
    │   ├── minicap
    │   └── minicap.so
    ├── x86/
    │   ├── minicap
    │   └── minicap.so
    └── x86_64/
        ├── minicap
        └── minicap.so
```

### 3. 文件权限

确保minicap文件具有可执行权限：
```bash
chmod 755 minicap
chmod 644 minicap.so
```

## 使用方法

### 1. 启动录制

```java
// 在MainActivity中点击"屏幕录制"按钮
// 或者直接启动ScreenRecordActivity
Intent intent = new Intent(context, ScreenRecordActivity.class);
startActivity(intent);
```

### 2. 录制控制

在ScreenRecordActivity中：
- 点击"开始录制"按钮启动录制
- 点击"停止录制"按钮结束录制
- 实时查看性能监控数据
- 查看详细的操作日志

### 3. 文件输出

录制完成的视频文件将保存到：
- Android 10+: `Movies/ScreenRecords/` (MediaStore)
- Android 9-: `应用外部存储/ScreenRecords/`

文件命名格式: `screen_record_yyyyMMdd_HHmmss.mp4`

## 性能数据分析

### 1. 监控指标说明

**CPU使用率:**
- 范围: 0-100%
- 计算方式: 应用CPU时间 / 系统总CPU时间
- 正常范围: 录制时通常在10-30%

**PSS内存:**
- 单位: KB
- 含义: 进程实际占用的物理内存
- 监控目的: 检测内存泄漏

**私有脏内存:**
- 单位: KB
- 含义: 进程独占的已修改内存页
- 监控目的: 评估内存使用效率

### 2. 性能优化建议

**CPU优化:**
- 降低录制帧率 (默认30fps)
- 调整视频编码参数
- 优化帧处理逻辑

**内存优化:**
- 限制帧队列大小
- 及时释放Bitmap资源
- 监控内存增长趋势

### 3. 日志分析

通过Logcat查看性能数据：
```bash
# 过滤性能监控日志
adb logcat -s PerformanceMonitor

# 过滤录制服务日志
adb logcat -s ScreenRecordService

# 过滤minicap相关日志
adb logcat -s MinicapManager:* MinicapDataCapture:*
```

## 故障排除

### 1. 常见问题

**minicap启动失败:**
- 检查设备ABI是否支持
- 确认文件权限设置正确
- 查看/data/local/tmp/目录权限

**录制无画面:**
- 确认minicap进程正常运行
- 检查本地套接字连接状态
- 验证屏幕参数获取是否正确

**性能监控数据异常:**
- 确认权限已正确授予
- 检查/proc/文件系统访问权限
- 验证进程PID获取是否正确

### 2. 调试方法

**启用详细日志:**
```java
// 在Application中设置日志级别
Log.setLogLevel(Log.VERBOSE);
```

**手动测试minicap:**
```bash
# 进入设备shell
adb shell

# 手动启动minicap
cd /data/local/tmp/
LD_LIBRARY_PATH=. ./minicap -P 1920x1080@1920x1080/0 -S
```

**检查进程状态:**
```bash
# 查看minicap进程
adb shell ps | grep minicap

# 查看套接字状态
adb shell netstat -an | grep minicap
```

## 安全注意事项

1. **权限控制:** 确保只在授权环境下使用录制功能
2. **文件安全:** 录制文件可能包含敏感信息，注意存储安全
3. **性能影响:** 长时间录制可能影响设备性能和电池续航
4. **隐私保护:** 遵守相关法律法规，保护用户隐私

## 扩展功能

### 1. 音频录制

可以扩展支持音频录制：
```java
// 在MediaMuxer中添加音频轨道
MediaFormat audioFormat = MediaFormat.createAudioFormat(MediaFormat.MIMETYPE_AUDIO_AAC, sampleRate, channelCount);
int audioTrackIndex = muxer.addTrack(audioFormat);
```

### 2. 实时流传输

可以扩展支持RTMP推流：
```java
// 使用第三方库如librtmp
// 将编码后的数据推送到流媒体服务器
```

### 3. 自定义编码参数

根据需求调整编码参数：
```java
// 在ScreenRecordService中修改
private static final int FRAME_RATE = 60; // 提高帧率
private static final int BIT_RATE_MULTIPLIER = 8; // 提高码率
```

## 版本历史

- v1.0.0: 初始版本，基础录制功能
- v1.1.0: 添加性能监控
- v1.2.0: 优化UI界面和错误处理

## 技术支持

如有问题，请查看：
1. 项目README文档
2. 相关源码注释
3. STF官方文档: https://github.com/openstf/stf
4. Android MediaCodec文档
