package com.xiaopeng.xpautotest.bean;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.xiaopeng.xpautotest.community.bean.CaseExecuteState;

import java.util.ArrayList;

public class TestTaskBean {

    @SerializedName("id")
    private long id;

    @SerializedName("vehicleId")
    private long vehicleId;

    @SerializedName("name")
    private String name;

    @SerializedName("status")
    private String status;

    @SerializedName("executionId")
    private long executionId;

    @SerializedName("suiteId")
    private long suiteId;

    @SerializedName("scriptDownloadUrl")
    private String scriptDownloadUrl;

    @SerializedName("autoStart")
    private boolean autoStart;
    @SerializedName("autoStartSuiteId")
    private long autoStartSuiteId;
    @SerializedName("autoStartTime")
    private long autoStartTime;
    @SerializedName("blackActivityNames")
    private ArrayList<String> blackActivityNames;
    private int totalCases;
    private int passedCases;
    private int failedCases;

    public long getId() {
        return id;
    }

    public long getVehicleId() {
        return vehicleId;
    }

    public String getName() {
        if (!TextUtils.isEmpty(name)) {
            name = name.trim();
        }
        return name;
    }

    public String getStatus() {
        return status;
    }
    public long getExecutionId() {
        return executionId;
    }
    public void setExecutionId(long executionId) {
        this.executionId = executionId;
    }
    public long getSuiteId() {
        return suiteId;
    }
    public void setSuiteId(long suiteId) {
        this.suiteId = suiteId;
    }
    public String getScriptDownloadUrl() {
        return scriptDownloadUrl;
    }
    public boolean isReady() {
        return scriptDownloadUrl != null && !scriptDownloadUrl.isEmpty();
    }

    public boolean isAutoStart() {
        return autoStart;
    }

    public long getAutoStartSuiteId() {
        return autoStartSuiteId;
    }

    public long getAutoStartTime() {
        return autoStartTime;
    }

    public ArrayList<String> getBlackActivityNames() {
        return blackActivityNames;
    }

    public boolean isFinished() {
        return totalCases > 0 && (passedCases + failedCases) >= totalCases;
    }

    /**
     * 是否可以自动启动执行：
     * 1. 启动标志为True
     * 2. 脚本下载完成
     * 3. 当前任务执行完成，或者没有执行过
     * */
    public boolean canAutoStart() {
        return isAutoStart() && isReady() && (isFinished() || (passedCases + failedCases) == 0);
    }

    public void initCaseCount() {
        this.totalCases = 0;
        this.passedCases = 0;
        this.failedCases = 0;
    }

    public void addCaseCount(int status) {
        this.totalCases++;
        if (status == CaseExecuteState.SUCCESS) {
            this.passedCases++;
        } else if (status == CaseExecuteState.FAILURE) {
            this.failedCases++;
        } else {
            this.failedCases++;
        }
    }

    public void addExecuteCount(int status) {
        if (status == CaseExecuteState.SUCCESS) {
            this.passedCases++;
        } else if (status == CaseExecuteState.FAILURE) {
            this.failedCases++;
        } else {
            this.failedCases++;
        }
    }

    public int getTotalCases() {
        return totalCases;
    }
    public void setTotalCases(int totalCases) {
        this.totalCases = totalCases;
    }

    public int getPassedCases() {
        return passedCases;
    }

    public void setPassedCases(int passedCases) {
        this.passedCases = passedCases;
    }

    public int getFailedCases() {
        return failedCases;
    }

    public void setFailedCases(int failedCases) {
        this.failedCases = failedCases;
    }

    public String getProgress() {
        return passedCases + "/" + totalCases;
    }

    public String toString() {
        return "TestTaskBean{id=" + id + ", name='" + name + "', status='" + status + "', executionId=" + executionId + ", autoStart='" + autoStart + ", autoStartSuiteId=" + autoStartSuiteId + "', total=" + totalCases +"'}";
    }
}
