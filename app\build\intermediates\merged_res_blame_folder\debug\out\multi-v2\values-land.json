{"logs": [{"outputFile": "com.xiaopeng.xpautotest.app-mergeDebugResources-45:/values-land/values-land.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fc66c71e6d2f7749ba39454a10f3998a\\transformed\\jetified-lib_xpui-5.6.3\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4,5,6,9,16", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,136,210,286,370,514,890", "endLines": "2,3,4,5,8,15,27", "endColumns": "80,73,75,83,12,12,12", "endOffsets": "131,205,281,365,509,885,1525"}, "to": {"startLines": "23,24,25,26,50,53,60", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1571,1652,1726,1802,3256,3400,3776", "endLines": "23,24,25,26,52,59,71", "endColumns": "80,73,75,83,12,12,12", "endOffsets": "1647,1721,1797,1881,3395,3771,4411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9fb248392f055438974c3c6bf62bfc1\\transformed\\appcompat-1.7.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\93d9df77809f9ff1d7d7a500adecfe6e\\transformed\\material-1.12.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,26,35,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,204,277,347,419,489,554,621,691,763,832,901,983,1073,1149,1217,1284,1362,1427,1494,1666,2235,2504", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,25,34,39,42", "endColumns": "74,73,72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10,10", "endOffsets": "125,199,272,342,414,484,549,616,686,758,827,896,978,1068,1144,1212,1279,1357,1422,1489,1661,2230,2499,2727"}, "to": {"startLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,27,28,29,33,42,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,339,413,486,556,628,698,763,830,900,972,1041,1110,1192,1282,1358,1426,1493,1886,1951,2018,2190,2759,3028", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,27,28,32,41,46,49", "endColumns": "74,73,72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10,10", "endOffsets": "334,408,481,551,623,693,758,825,895,967,1036,1105,1187,1277,1353,1421,1488,1566,1946,2013,2185,2754,3023,3251"}}]}]}