package com.xiaopeng.xpautotest.utils;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import java.io.InputStream;

/**
 * minicap文件验证器
 * 用于检查minicap文件是否正确部署
 */
public class MinicapValidator {
    private static final String TAG = "MinicapValidator";
    
    private Context context;
    
    public MinicapValidator(Context context) {
        this.context = context;
    }
    
    /**
     * 验证minicap文件
     */
    public ValidationResult validateMinicapFiles() {
        ValidationResult result = new ValidationResult();
        
        try {
            // 获取设备ABI
            String abi = getDeviceAbi();
            result.deviceAbi = abi;
            
            Log.d(TAG, "Validating minicap files for ABI: " + abi);
            
            // 检查minicap文件
            String minicapPath = "minicap/" + abi + "/minicap";
            result.minicapExists = checkAssetFile(minicapPath);
            if (result.minicapExists) {
                result.minicapSize = getAssetFileSize(minicapPath);
            }
            
            // 检查minicap.so文件
            String minicapSoPath = "minicap/" + abi + "/minicap.so";
            result.minicapSoExists = checkAssetFile(minicapSoPath);
            if (result.minicapSoExists) {
                result.minicapSoSize = getAssetFileSize(minicapSoPath);
            }
            
            // 检查其他架构
            String[] allAbis = {"arm64-v8a", "armeabi-v7a", "x86", "x86_64"};
            for (String checkAbi : allAbis) {
                if (!checkAbi.equals(abi)) {
                    boolean hasMinicap = checkAssetFile("minicap/" + checkAbi + "/minicap");
                    boolean hasMinicapSo = checkAssetFile("minicap/" + checkAbi + "/minicap.so");
                    if (hasMinicap && hasMinicapSo) {
                        result.alternativeAbis.add(checkAbi);
                    }
                }
            }
            
            result.isValid = result.minicapExists && result.minicapSoExists;
            
        } catch (Exception e) {
            Log.e(TAG, "Error validating minicap files", e);
            result.error = e.getMessage();
        }
        
        return result;
    }
    
    /**
     * 检查assets文件是否存在
     */
    private boolean checkAssetFile(String assetPath) {
        try {
            InputStream inputStream = context.getAssets().open(assetPath);
            inputStream.close();
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取assets文件大小
     */
    private long getAssetFileSize(String assetPath) {
        try {
            InputStream inputStream = context.getAssets().open(assetPath);
            long size = inputStream.available();
            inputStream.close();
            return size;
        } catch (Exception e) {
            return 0;
        }
    }
    
    /**
     * 获取设备ABI
     */
    private String getDeviceAbi() {
        String abi = Build.SUPPORTED_ABIS[0];
        switch (abi) {
            case "arm64-v8a":
                return "arm64-v8a";
            case "armeabi-v7a":
                return "armeabi-v7a";
            case "x86":
                return "x86";
            case "x86_64":
                return "x86_64";
            default:
                Log.w(TAG, "Unsupported ABI: " + abi + ", fallback to arm64-v8a");
                return "arm64-v8a";
        }
    }
    
    /**
     * 验证结果
     */
    public static class ValidationResult {
        public String deviceAbi;
        public boolean minicapExists = false;
        public boolean minicapSoExists = false;
        public long minicapSize = 0;
        public long minicapSoSize = 0;
        public java.util.List<String> alternativeAbis = new java.util.ArrayList<>();
        public boolean isValid = false;
        public String error;
        
        public String getReport() {
            StringBuilder report = new StringBuilder();
            report.append("=== Minicap文件验证报告 ===\n");
            report.append("设备架构: ").append(deviceAbi).append("\n");
            report.append("minicap文件: ").append(minicapExists ? "✅ 存在" : "❌ 缺失");
            if (minicapExists) {
                report.append(" (").append(minicapSize).append(" bytes)");
            }
            report.append("\n");
            
            report.append("minicap.so文件: ").append(minicapSoExists ? "✅ 存在" : "❌ 缺失");
            if (minicapSoExists) {
                report.append(" (").append(minicapSoSize).append(" bytes)");
            }
            report.append("\n");
            
            if (!alternativeAbis.isEmpty()) {
                report.append("其他可用架构: ").append(String.join(", ", alternativeAbis)).append("\n");
            }
            
            report.append("验证结果: ").append(isValid ? "✅ 通过" : "❌ 失败").append("\n");
            
            if (error != null) {
                report.append("错误信息: ").append(error).append("\n");
            }
            
            if (!isValid) {
                report.append("\n=== 解决建议 ===\n");
                if (!minicapExists) {
                    report.append("1. 下载minicap文件并放置到: app/src/main/assets/minicap/").append(deviceAbi).append("/minicap\n");
                }
                if (!minicapSoExists) {
                    report.append("2. 下载minicap.so文件并放置到: app/src/main/assets/minicap/").append(deviceAbi).append("/minicap.so\n");
                }
                report.append("3. 从STF项目获取文件: https://github.com/openstf/stf/releases\n");
                report.append("4. 或使用部署脚本: deploy_minicap.bat / deploy_minicap.sh\n");
            }
            
            return report.toString();
        }
    }
}
