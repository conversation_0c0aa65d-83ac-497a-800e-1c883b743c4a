package com.xiaopeng.xpautotest.manager;

import android.car.XpCarFeatures;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xiaopeng.lib.utils.ThreadUtils;
import com.xiaopeng.xpautotest.App;
import com.xiaopeng.xpautotest.bean.HeartbeatEntity;
import com.xiaopeng.xpautotest.bean.VehicleInfo;
import com.xiaopeng.xpautotest.client.api.VehicleApiImpl;
import com.xiaopeng.xpautotest.community.event.StopTestEvent;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.community.bean.CaseExecuteState;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.bean.ErrorCode;
import com.xiaopeng.xpautotest.bean.TestCaseBean;
import com.xiaopeng.xpautotest.bean.TestResultEntity;
import com.xiaopeng.xpautotest.bean.TestSuiteBean;
import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.client.ClientApiRepository;
import com.xiaopeng.xpautotest.client.api.ApiException;
import com.xiaopeng.xpautotest.client.api.ApiResponse;
import com.xiaopeng.xpautotest.client.api.TaskApiImpl;
import com.xiaopeng.xpautotest.client.error.ErrorHandler;
import com.xiaopeng.xpautotest.helper.TestDataStorageHelper;
import com.xiaopeng.xpautotest.model.ITestDataState;
import com.xiaopeng.xpautotest.model.ITestSuiteItem;
import com.xiaopeng.xpautotest.model.TestCaseItem;
import com.xiaopeng.xpautotest.model.TestSuiteItem;
import com.xiaopeng.xpautotest.service.TestExecutionService;
import com.xiaopeng.xpautotest.utils.ApiRouterUtils;
import com.xiaopeng.xpautotest.utils.CarUtils;
import com.xiaopeng.xpautotest.utils.FileUtils;
import com.xiaopeng.xpautotest.utils.GsonUtils;
import com.xiaopeng.xpautotest.utils.Rx2Util;
import com.xiaopeng.xpautotest.utils.ServiceUtils;
import com.xiaopeng.xpautotest.utils.SystemPropertiesUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import lombok.Getter;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class TestManager {
    private static final String TAG = "TestManager";
    private static final String CACHE_DIR = "/topic/topic_list.json";
    private static List<ITestSuiteItem> sSuiteList;
    private ErrorCode mErrorCode;
    private TestTaskBean mTaskInfo;

    @Getter
    private final VehicleInfo vehicleInfo;
    private static List<String> sTopicTitleList = new ArrayList<>();
    private final ConnectionChangeReceiver.INetworkMsgHandler mINetworkMsgHandler = this::onNetWorkConnect;
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private ClientApiRepository clientApiRepository;
    private boolean mIsNeedRegister = true;
    private boolean mIsNeedUpdate = false;

    private TestManager() {
        ThreadUtils.execute(() -> {
            ConnectionChangeReceiver.getInstance().addNetworkMsgHandler(mINetworkMsgHandler);
        });
        initClientApi();

        // 工厂模式还是正常模式
        String mode = ApiRouterUtils.isFactoryMode() ? "factory" : "normal";
        vehicleInfo = new VehicleInfo(SystemPropertiesUtils.getVIN(), CarUtils.getCarType(),
                XpCarFeatures.getSoftwareVersionCode(), SystemPropertiesUtils.getSID(), "", mode);
        FileLogger.d(TAG, "init vehicle info. " + vehicleInfo);
//        if (!ApiRouterUtils.isFactoryMode()) {
//            scheduler.scheduleWithFixedDelay(this::heartbeat, 5, 30, TimeUnit.SECONDS);
//        }
        new HeartbeatManager().startHeartbeat(this::performHeartbeatSync);
    }

    private static class SingletonHolder {
        private static final TestManager Instance = new TestManager();
    }

    public static TestManager getInstance() {
        return SingletonHolder.Instance;
    }

    public void load(String from) {
        if (from.equals("remote")) {
            FileLogger.i(TAG, "load from remote");
            this.mIsNeedUpdate = false;
            register();
        }else if(from.equals("force_remote")){
            FileLogger.i(TAG," force load from remote");
            this.mIsNeedUpdate = true;
            register();
        }else {
            FileLogger.i(TAG, "load from local");
            load();
        }
    }

    public void load() {
        String topicPath = Constant.AUTOTEST_SCRIPT_LIST_FILE;
        final String topicFilePath = topicPath;
        Log.i(TAG, "load, testcase path = " + topicPath);
        if (mTaskInfo == null) {
            Log.d(TAG, "Memory task is null. Loading from local storage");
            mTaskInfo = TestDataStorageHelper.getInstance().loadTaskSync();
        }
        Disposable topicDisposable = Rx2Util
                .getFlowableOnIo(() -> {
                    Gson gson = new Gson();

                    List<TestSuiteBean> testSuiteItems = null;
                    String fileString = FileUtils.readFile(topicFilePath);
                    try {
                        testSuiteItems = gson.fromJson(fileString, new TypeToken<List<TestSuiteBean>>() {
                        }.getType());
                        Log.d(TAG, "load from sdcard: asset!!!");
                    } catch (Exception e) {
                        Log.e(TAG, "Failed to parse test suite items from JSON: " + e.getMessage());
                        e.printStackTrace();
                    }
                    if (testSuiteItems == null) {
                        testSuiteItems = new ArrayList<>();
                        Log.e(TAG, "load testcase list: null!!!");
                    }

                    List<ITestSuiteItem> suiteList = new ArrayList<>();
                    List<String> topicTitleList = new ArrayList<>();
                    List<String> videoKeyList = new ArrayList<>();

                    // 从文件中获取测试结果以便赋值给前端展示
                    Map<Long, TestResultEntity> testResultMap = TestDataStorageHelper.getInstance().loadAllMapResultsSync();
                    Log.d(TAG, "Loaded test results from storage. Result count: " + testResultMap.size());

                    try {
                        int index = 0;
                        mTaskInfo.initCaseCount();
                        for (TestSuiteBean bean : testSuiteItems) {
                            // 标题
                            TestSuiteItem testSuiteItem = new TestSuiteItem();
                            testSuiteItem.setId(bean.getId());
                            testSuiteItem.setName(bean.getName());
                            testSuiteItem.setStatus(bean.getStatus());

                            suiteList.add(testSuiteItem);
                            topicTitleList.add(testSuiteItem.getName());

                            List<TestCaseBean> items = bean.getItems();
                            if (items != null) {
                                int relativeIndex = 1;
                                int suiteStatus = CaseExecuteState.SUCCESS;
                                for (TestCaseBean testCaseBean : items) {
                                    if (testCaseBean != null) {
                                        TestCaseItem caseItem = createTestCaseItem(testCaseBean, testSuiteItem.getId());
                                        if (testResultMap.containsKey(caseItem.getScriptId())) {
                                            TestResultEntity resultEntity = testResultMap.get(caseItem.getScriptId());
                                            caseItem.setStatus(resultEntity.getStatus());
                                            caseItem.setTaskExecutionId(resultEntity.getTaskExecutionId());
                                        }

                                        // 添加组别
                                        List<TestCaseItem> caseItems = testSuiteItem.getItems();
                                        if (caseItems == null) {
                                            caseItems = new ArrayList<>();
                                            testSuiteItem.setItems(caseItems);
                                        }
                                        caseItems.add(caseItem);
                                        mTaskInfo.addCaseCount(caseItem.getStatus());
                                        if (caseItem.getStatus() == CaseExecuteState.SUCCESS ||
                                                caseItem.getStatus() == CaseExecuteState.SKIPPED) {
                                            // 成功或跳过不修改
                                        } else if (caseItem.getStatus() == CaseExecuteState.FAILURE) {
                                            suiteStatus = CaseExecuteState.FAILURE;
                                        } else if (caseItem.getStatus() == CaseExecuteState.EXECUTING) {
                                            suiteStatus = CaseExecuteState.EXECUTING;
                                        } else {
                                            suiteStatus = CaseExecuteState.PENDING;
                                        }
                                    }
                                }
                                testSuiteItem.setStatus(suiteStatus);
                            }

                            sTopicTitleList = topicTitleList;
                            index++;
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "load Topic error :" + e);
                    }
                    Log.i(TAG, "loadConfig end videoKeyList.size():" + videoKeyList.size()
                            + ",suiteList.size():" + suiteList.size());
                    return suiteList;
                })
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<List<ITestSuiteItem>>() {
                    @Override
                    public void accept(List<ITestSuiteItem> suiteList) throws Exception {
                        sSuiteList = suiteList;
                        if (sSuiteList == null) {
                            Log.e(TAG, "loadConfig sSuiteList=null !!!");
                        } else {
                            Log.i(TAG, "loadConfig sSuiteList.size():" + sSuiteList.size());
                        }
                        if (mSuiteListCallBack == null) {
                            Log.e(TAG, "loadConfig mSuiteListCallBack=null !!!");
                        }
                        if (mSuiteListCallBack != null) {
                            // todo 通知取消loading，或显示错误信息
                            mSuiteListCallBack.taskInfoSuccess();
                            mSuiteListCallBack.suiteListSuccess();
                        }
                    }
                });
    }

    public interface ITestDataCallBack<T> {
        void onUpdate(T data);
    }

    private ISuiteListCallBack mSuiteListCallBack;

    public interface ISuiteListCallBack {
        void taskInfoSuccess();
        void suiteListSuccess();
        void loadCaseFailed();
        void triggerAutoStart();
    }

    public void setISuiteListCallBack(ISuiteListCallBack callBack) {
        mSuiteListCallBack = callBack;
    }

    private TestCaseItem createTestCaseItem(TestCaseBean caseBean, long suiteId) {
        TestCaseItem caseItem = new TestCaseItem(caseBean.getCaseId(), caseBean.getScriptId(), suiteId, caseBean.getName(), caseBean.getResult(), caseBean.getLastRunTime());
        caseItem.setStatus(caseBean.getStatus());
        caseItem.setSteps(caseBean.getSteps());
        return caseItem;
    }

    public List<ITestSuiteItem> getSuiteList() {
        if (sSuiteList != null) {
            return sSuiteList;
        }
        Log.e(TAG, "getSuiteList: sSuiteList=null !!!");
        return new ArrayList<>();
    }

    public List<String> getTopicTitleList() {
        if (sTopicTitleList != null) {
            return sTopicTitleList;
        }
        return null;
    }

    public boolean saveTopics(List<TestSuiteBean> topics) {
        Log.i(TAG, "saveTopics");
        if (topics != null && topics.size() > 0) {
            String cachePath = getCachePath();
            String oldData = FileUtils.readFile(cachePath);
            String data = GsonUtils.toJson(topics, TAG);
            Log.i(TAG, "data:" + data + ",oldData:" + oldData + ",cachePath:" + cachePath);
            if (!TextUtils.isEmpty(data) && !data.equals(oldData)) {
                FileUtils.saveDataToFile(cachePath, data);
                return true;
            }
        }
        return false;
    }

    private String getCachePath() {
        String cachePath = App.getInstance().getFilesDir().getAbsolutePath() + CACHE_DIR;
        Log.d(TAG, "getCachePath: cachePath = " + cachePath);
        return cachePath;
    }

    public void update() {
        Log.i(TAG, "update");
    }

    public void onNetWorkConnect(boolean state) {
        FileLogger.i(TAG, "onNetWorkConnect state: " + state);
        if (state) {
            // wifi连接时，才需要注册
            if (ConnectionChangeReceiver.getInstance().isWifiConnected()){
                if(mIsNeedRegister){
                    FileLogger.i(TAG, "onNetWorkConnect: wifi connected, register");
                    register();
                }else if (!isExecutionServiceRunning()) {
                    // 网络重连以后，如果测试服务没有运行，迅速刷新任务，autostart的任务可以继续跑起来
                    FileLogger.i(TAG, "onNetWorkConnect: heartbeat() to refresh task");
//                    performHeartbeatSync();   // 暂时不需要心跳
                } else {
                    Log.w(TAG, "onNetWorkConnect: TestExecutionService is running, skipping heartbeat");
                }
            } else {
                Log.w(TAG, "onNetWorkConnect: Not connected to WiFi, skipping register");
            }
        }
    }

    private void initClientApi() {
        if (clientApiRepository != null) {
            return;
        }
        clientApiRepository = new ClientApiRepository(new ClientApiRepository.TestApiResponseListener() {
            @Override
            public void onTaskInfoReceived(TestTaskBean taskInfo) {
                processTaskUpdate(taskInfo, "Register");
                mIsNeedRegister = false;
            }
            @Override
            public void onRegisterFailure(Throwable t) {
                mErrorCode = new ErrorHandler(App.getInstance()).handleError(t);
                Log.e(TAG, "onFailure: " + mErrorCode);
                handleInvalidTaskFromServer("Register", mErrorCode);
            }

            @Override
            public void onHeartbeatTaskInfoReceived(TestTaskBean taskInfo) {
                processTaskUpdate(taskInfo, "Heartbeat");
            }

            @Override
            public void onHeartbeatFailure(Throwable t) {
                mErrorCode = new ErrorHandler(App.getInstance()).handleError(t);
                Log.e(TAG, "onFailure: " + mErrorCode);
                handleInvalidTaskFromServer("Heartbeat", mErrorCode);
            }

            @Override
            public void onDownloadFailure(Throwable t) {
                mErrorCode = new ErrorHandler(App.getInstance()).handleError(t);
                Log.e(TAG, "onFailure: " + mErrorCode);
                handleInvalidTaskFromServer("Download", mErrorCode);
            }

            @Override
            public void onTestStarted(long executionId) {
                Log.i(TAG, "onTestStarted: executionId=" + executionId);
                mTaskInfo.setExecutionId(executionId);
                TestDataStorageHelper.getInstance().saveTask(mTaskInfo, success -> {});
                if (mSuiteListCallBack != null) {
                    mSuiteListCallBack.taskInfoSuccess();
                }
            }

            @Override
            public void onScriptsDownloaded() {
                FileLogger.i(TAG, "onScriptsDownloaded: Scripts for current mTaskInfo (ID: " + (mTaskInfo != null ? mTaskInfo.getId() : "null") + ") downloaded.");
                load();
            }

            @Override
            public void onFailure(Throwable t) {
                mErrorCode = new ErrorHandler(App.getInstance()).handleError(t);
                Log.e(TAG, "onFailure: " + mErrorCode);
                handleInvalidTaskFromServer("OnFailure", mErrorCode);
            }
        });
    }

    /**
     * 核心逻辑：处理来自服务器（注册或心跳）的任务信息更新请求。
     *
     * @param newTaskInfoFromServer 从服务器获取的最新任务信息。
     * @param source                请求来源 ("Register"、"Heartbeat")
     */
    private void processTaskUpdate(TestTaskBean newTaskInfoFromServer, String source) {
        // 场景: 如果当前有测试正在执行，则忽略本次任务更新，以防止中断当前测试。
        if (isExecutionServiceRunning()) {
            Log.i(TAG, "Test execution service is running. Ignoring task update from " + source + " to prevent interruption.");
            return;
        }

        // 程序执行到此处，表示 newTaskInfoFromServer 是一个有效的、非空且"准备就绪"的任务。
        TestTaskBean oldPersistedTask = TestDataStorageHelper.getInstance().loadTaskSync();
        
        // 强制更新标志优先
        if(mIsNeedUpdate){
            Log.i(TAG, "Force update flag is set. Applying new task and downloading scripts.");
            applyNewTaskAndDownloadScripts(newTaskInfoFromServer);
            this.mIsNeedUpdate = false;
            return;
        }

        // 场景：比较服务器新任务与本地持久化任务
        if (oldPersistedTask != null && oldPersistedTask.isReady() && oldPersistedTask.getId() == newTaskInfoFromServer.getId()) {
            Log.d(TAG, "Task IDs match. Comparing execution states...");

            // 新任务信息携带AutoStart标志，旧任务未携带，则保存新任务并自动启动
            if (newTaskInfoFromServer.isAutoStart() != oldPersistedTask.isAutoStart()) {
                mTaskInfo = newTaskInfoFromServer;
                TestDataStorageHelper.getInstance().saveTask(mTaskInfo, success -> {});

                if (mSuiteListCallBack != null && !isExecutionServiceRunning()) {
                    if (source.equals("Heartbeat")) {
                        // 如果是心跳来源且需要自动启动，则自动执行任务
                        mSuiteListCallBack.triggerAutoStart();
                        Log.i(TAG, "Heartbeat source: Triggering auto-start callback for task ID: " + newTaskInfoFromServer.getId());
                    } else {
                        // 如果是注册来源，则更新内存中的任务信息并加载UI，onSuiteListUpdate中走自启动逻辑
                        Log.i(TAG, "Register source: Updating task info in memory and loading UI for task ID: " + newTaskInfoFromServer.getId());
                        load();
                    }
                    return;
                }
            }

            // 任务ID相同。现在根据来源 (source) 和旧任务的执行状态来区分处理。
            // 旧任务是否已执行
            boolean oldTaskWasExecuted = oldPersistedTask.getExecutionId() > 0;
            // 新任务的执行ID等于0，表示这是一个新任务，或者是服务端记录的已经执行完成闭环的任务
            boolean newTaskExecutionIdIsZero = newTaskInfoFromServer.getExecutionId() == 0;
            // executionId是否相等
            boolean executionIdsAreEqual = Objects.equals(oldPersistedTask.getExecutionId(), newTaskInfoFromServer.getExecutionId());

            Log.i(TAG, "Task state analysis: oldExecuted=" + oldTaskWasExecuted +
                    ", newExecutionIdIsZero=" + newTaskExecutionIdIsZero + ", executionIdsEqual=" + executionIdsAreEqual);

            // 场景1: 本地任务已执行，服务器状态完全一致。
            // 心跳：如果新任务需要自动启动，则触发UI刷新驱动任务启动，否则静默。注册：刷新UI。
            if (oldTaskWasExecuted && executionIdsAreEqual) {
                Log.i(TAG, "Scenario 1: Local task executed and server state matches. Handling synced task.");
                handleSyncedTask(oldPersistedTask, newTaskInfoFromServer, source);
                return;
            }

            // 场景2: 本地任务已执行，但服务器返回了一个"重置"的任务（执行ID为0）。
            // 要保留本地的执行结果，而不是用一个"新"任务覆盖它。心跳：如果新任务需要自动启动，则触发UI刷新驱动任务启动，否则静默。注册：刷新UI。
            if (oldTaskWasExecuted && newTaskExecutionIdIsZero) {
                Log.i(TAG, "Scenario 2: Local task executed but server returned reset task (executionId=0). Preserving local results.");
                handleSyncedTask(oldPersistedTask, newTaskInfoFromServer, source);
                return;
            }

            // 场景3: 执行ID不同
            // 可能是某些原因本地任务执行ID没更新，只需更新执行ID，无需重新下载脚本
            if (!executionIdsAreEqual) {
                Log.i(TAG, "Scenario 3: Execution IDs mismatch. Updating local execution ID.");
                handleExecutionIdMismatch(oldPersistedTask, newTaskInfoFromServer, source);
                return;
            }

            // 场景4: 其他情况。
            // 如果是注册来源，那么就load()，如果是心跳来源那么就静默
            Log.i(TAG, "Scenario 4: Other cases. load() or silent update based on source.");
            if(source.equals("Register")) {
                this.mTaskInfo = oldPersistedTask;
                load();
            }
            return;
        }

        // --- 通用更新逻辑 ---
        // 如果代码执行到这里，说明：
        // 1. 本地没有持久化的任务。
        // 2. 本地有任务，但ID与服务器返回的不同。
        // 在这些情况下，需要用服务器的任务信息来更新本地。
        Log.i(TAG, "General update scenario: No local task or task ID mismatch. Applying new task from server.");
        applyNewTaskAndDownloadScripts(newTaskInfoFromServer);
    }

    /**
     * 处理任务状态匹配的情况。
     * 如果是注册来源，则更新内存中的任务信息并加载。
     * 如果是心跳来源，且新任务需要自动启动，则触发UI刷新以进行决策。
     */
    private void handleSyncedTask(TestTaskBean oldPersistedTask, TestTaskBean newTaskInfoFromServer, String source) {
        if ("Register".equals(source)) {
            Log.i(TAG, "Register source: updating memory task and loading UI");
            this.mTaskInfo = oldPersistedTask;
            load();
        } else if ("Heartbeat".equals(source)) {
            Log.d(TAG, "Heartbeat source: checking autoStart requirement");
            // 如果是心跳，且旧任务需要自动启动，则延迟3秒后触发回调驱动任务启动
            if (oldPersistedTask.isAutoStart()) {
                Log.i(TAG, "Task requires autoStart. Triggering delayed auto-start callback");
                triggerAutoStartWithDelay();
            } else {
                Log.d(TAG, "Task does not require autoStart. No action needed");
            }
        }
    }

    /**
     * 处理执行ID不匹配的情况。
     * 更新本地任务的执行ID，并保存到持久化存储。
     * 如果是注册来源，则需要刷新UI以显示正确状态。
     */
    private void handleExecutionIdMismatch(TestTaskBean oldPersistedTask, TestTaskBean newTaskInfoFromServer, String source) {
        if (this.mTaskInfo == null || this.mTaskInfo.getId() != oldPersistedTask.getId()) {
            Log.d(TAG, "Updating memory task reference to match persisted task");
            this.mTaskInfo = oldPersistedTask;
        }

        Log.d(TAG, "Updating execution ID from " + this.mTaskInfo.getExecutionId() + " to " + newTaskInfoFromServer.getExecutionId());
        this.mTaskInfo.setExecutionId(newTaskInfoFromServer.getExecutionId());

        TestDataStorageHelper.getInstance().saveTask(this.mTaskInfo, success -> {
            if (success) {
                Log.i(TAG, "Successfully saved updated execution ID for task " + this.mTaskInfo.getId());
            } else {
                Log.e(TAG, "Failed to save updated execution ID for task " + this.mTaskInfo.getId());
            }
        });

        // 对于注册来源，更新ID后需要刷新UI以显示正确状态
        if ("Register".equals(source)) {
            Log.i(TAG, "Register source: refreshing UI after execution ID update");
            load();
        } else {
            Log.d(TAG, "Non-register source: execution ID updated without UI refresh");
        }
    }

    /**
     * 处理网络故障、从服务器收到的空或无效任务
     * @param source 任务更新的来源 ("Register", "Heartbeat", "OnFailure")
     * @param errorCode 错误码，用于判断是服务端无任务还是网络异常
     */
    private void handleInvalidTaskFromServer(String source, ErrorCode errorCode) {
        boolean isMemoryClean = this.mTaskInfo == null && (sSuiteList == null || sSuiteList.isEmpty());
        boolean shouldClearLocalTask = errorCode == ErrorCode.ERROR_API_REGISTER_NO_TASK;
        boolean isRegisterSource = "Register".equals(source);
        boolean isHeartbeatSource = "Heartbeat".equals(source);
        
        Log.i(TAG, String.format("Handling invalid task from %s, ErrorCode: %s, MemoryClean: %s, ShouldClear: %s",
                source, errorCode, isMemoryClean, shouldClearLocalTask));
        // 测试中的话，不处理
        if (isExecutionServiceRunning()) {
            Log.i(TAG, "Test execution service is running. Ignoring invalid task handling to prevent interruption.");
            return;
        }

        if (isMemoryClean) {
            handleCleanMemoryScenario(isRegisterSource);
        } else if (isRegisterSource) {
            handleRegisterSourceScenario(shouldClearLocalTask, errorCode);
        } else if(isHeartbeatSource) {
            handleHeartbeatSourceScenario(shouldClearLocalTask, errorCode);
        } else if ("Download".equals(source)) {
            // 处理下载脚本失败的场景
            Log.w(TAG, "Download script failed. Handling as a special case.");
            notifyUILoadFailed();
        } else{
            handleOtherSourceScenario(errorCode);
        }
    }

    /**
     * 处理内存已经干净的场景
     */
    private void handleCleanMemoryScenario(boolean isRegisterSource) {
        if (isRegisterSource) {
            Log.i(TAG, "Memory is clean, but Register API failed. Notifying UI to ensure it's not stuck.");
            notifyUILoadFailed();
        } else {
            Log.d(TAG, "Memory is already clean and received a null task. No action needed.");
        }
    }
    
    /**
     * 处理Register来源的场景
     */
    private void handleRegisterSourceScenario(boolean shouldClearLocalTask, ErrorCode errorCode) {
        if (shouldClearLocalTask) {
            Log.i(TAG, "Server has no task, Clearing local task.");
            clearAllTaskData();
        } else {
            handleNetworkErrorWithLocalFallback(errorCode);
        }
    }

    private void handleHeartbeatSourceScenario(boolean shouldClearLocalTask, ErrorCode errorCode) {
        if (shouldClearLocalTask) {
            Log.i(TAG, "Server has no task, Clearing local task.");
            clearAllTaskData();
        } else {
            // 如果旧任务需要自动启动，则延迟3秒后触发回调驱动任务启动
            if (this.mTaskInfo.isAutoStart()) {
                triggerAutoStartWithDelay();
            }
        }
    }
    
    /**
     * 处理其他来源的场景(onFailure：目前只有下载脚本失败)
     */
    private void handleOtherSourceScenario(ErrorCode errorCode) {
        Log.d(TAG, "handleOtherSourceScenario");
        handleNetworkErrorWithLocalFallback(errorCode);
    }
    
    /**
     * 网络异常时尝试优先加载本地任务
     */
    private void handleNetworkErrorWithLocalFallback(ErrorCode errorCode) {
        TestTaskBean localTask = TestDataStorageHelper.getInstance().loadTaskSync();
        if (localTask != null && localTask.isReady()) {
            Log.i(TAG, String.format("Network error, but valid local task found. Loading it. ErrorCode: %s", errorCode));
            this.mTaskInfo = localTask;
            load();
        } else {
            Log.w(TAG, String.format("Network error and no valid local task. ErrorCode: %s", errorCode));
            notifyUILoadFailed();
        }
    }
    
    /**
     * 清空所有任务数据
     */
    private void clearAllTaskData() {
        // 清理本地存储
        TestDataStorageHelper.getInstance().clearHistory(() -> {
            FileLogger.i(TAG, "Local task and scripts cleared.");
        });
        // 清理内存数据
        this.mTaskInfo = null;
        sSuiteList = new ArrayList<>();
        sTopicTitleList = new ArrayList<>();
        // 通知UI更新
        notifyUIAfterClear();
    }

    /**
     * 清理后通知UI更新
     */
    private void notifyUIAfterClear() {
        if (mSuiteListCallBack != null) {
            Log.d(TAG, "notifyUIAfterClear: notifying UI with empty suite list and null task info");
            mSuiteListCallBack.suiteListSuccess(); // 用空列表通知UI
            mSuiteListCallBack.taskInfoSuccess();  // 用null任务通知UI
            mSuiteListCallBack.loadCaseFailed();   // 触发加载失败的回调
        }
    }
    
    /**
     * 通知UI加载失败
     */
    private void notifyUILoadFailed() {
        if (mSuiteListCallBack != null) {
            Log.d(TAG, "notifyUILoadFailed: notifying UI of load failure");
            mSuiteListCallBack.loadCaseFailed();
        }
    }

    /**
     * 应用新的任务信息，并下载关联的脚本文件。
     * @param newTaskInfoToApply 要应用的新任务
     */
    private void applyNewTaskAndDownloadScripts(TestTaskBean newTaskInfoToApply) {
        Log.d(TAG, "applyNewTaskAndDownloadScripts");

        TestDataStorageHelper.getInstance().clearHistory(() -> {
            Log.d(TAG, "Local history cleared. Proceeding with script download and task save.");

            // 先下载脚本
            if (newTaskInfoToApply.getScriptDownloadUrl() != null && !newTaskInfoToApply.getScriptDownloadUrl().isEmpty()) {
                this.mTaskInfo = newTaskInfoToApply; // 立即用服务器的最新任务更新内存中的主任务对象。
                clientApiRepository.downloadScriptOSS(newTaskInfoToApply.getScriptDownloadUrl()); // 触发 onScriptsDownloaded -> load()
                // 再保存任务信息
                TestDataStorageHelper.getInstance().saveTask(newTaskInfoToApply, success -> {
                    if (success) {
                        Log.i(TAG, "Task saved to local storage successfully: taskId=" + newTaskInfoToApply.getId());
                    } else {
                        // 保存任务信息到本地存储失败的话，只记录日志，不影响后续流程。
                        Log.e(TAG, "Failed to save task to local storage: taskId=" + newTaskInfoToApply.getId());
                    }
                });
            } else {
                // 如果任务没有脚本下载URL，则不进行脚本下载，不更新任务
                Log.e(TAG, "No script download URL provided. Skipping script download for taskId=" +
                        (this.mTaskInfo != null ? this.mTaskInfo.getId() : "unknown"));
            }
        });
    }

    /**
     * 注册车辆信息到测试平台
     * */
    private void register() {
        // 重置错误码
        mErrorCode = null;
        clientApiRepository.register(vehicleInfo);
    }

    /**
     * 通知测试平台开始测试，同步请求
     * */
    public long startTestSync() {
        long taskExecutionId = -1;
        if (mTaskInfo == null) {
            Log.e(TAG, "startTest: mTaskInfo is null");
            return taskExecutionId;
        }

        TaskApiImpl taskApi = new TaskApiImpl();
        try{
            Response<ApiResponse<Long>> response = taskApi.startTestSync(mTaskInfo);
            if (response.isSuccessful()) {
                Log.i(TAG, "startTest onResponse: " + response.body());
                taskExecutionId = response.body().getData();  // 获取执行ID

                mTaskInfo.setExecutionId(taskExecutionId);
                if (mSuiteListCallBack != null) {
                    mSuiteListCallBack.taskInfoSuccess();
                }
                TestDataStorageHelper.getInstance().saveTask(mTaskInfo, success -> {});
            } else {
                Log.e(TAG, "startTest onResponse Server error: " + response.code());
            }
        } catch (Exception e) {
            Log.e(TAG, "startTestSync with exception: " + e.getMessage());
            e.printStackTrace();
        }
        return taskExecutionId;
    }

    /**
     * 带回调的通知测试平台开始测试方法
     * */
    public void startTest(ITestDataCallBack<ITestDataState<Long>> callback) {
        if (mTaskInfo == null) {
            Log.e(TAG, "startTest: mTaskInfo is null");
            return;
        }

        callback.onUpdate(new ITestDataState.Loading<>());
        TaskApiImpl taskApi = new TaskApiImpl();

        taskApi.startTest(mTaskInfo.getId(), mTaskInfo, new Callback<ApiResponse<Long>>() {
            @Override
            public void onResponse(Call<ApiResponse<Long>> call, Response<ApiResponse<Long>> response) {
                if (response.isSuccessful()) {
                    Log.i(TAG, "startTest onResponse: " + response.body());
                    long executionId = response.body().getData();  // 获取执行ID
                    callback.onUpdate(new ITestDataState.Success<>(executionId));

                    mTaskInfo.setExecutionId(executionId);
                    TestDataStorageHelper.getInstance().saveTask(mTaskInfo, success -> {});
                    if (mSuiteListCallBack != null) {
                        mSuiteListCallBack.taskInfoSuccess();
                    }
                } else {
                    Log.e(TAG, "startTest onResponse Server error: " + response.code());
//                    listener.onFailure(new ApiException(response.code(), "Server error: " + response.code()));
                    callback.onUpdate(new ITestDataState.Error<>(new ApiException(response.code(), "Server error: " + response.code())));
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Long>> call, Throwable t) {
                Log.e(TAG, "startTest onFailure: " + t.getMessage());
//                listener.onFailure(t);
                callback.onUpdate(new ITestDataState.Error<>(t));
            }
        });
    }

    /**
     * 通知测试平台停止测试
     * */
    public void stopTest() {
        clientApiRepository.stopTest(mTaskInfo);
    }

    /**
     * 上报测试进度到测试平台，并保存测试结果到本地
     * */
//    public void saveTestResult(TestResultEntity result) {
//        // 保存测试结果
//        if (mTaskInfo.getExecutionId() > 0) {
//            clientApiRepository.reportResult(mTaskInfo.getExecutionId(), result);
//        } else {
//            Log.e(TAG, "skip report test result: executionId=" + mTaskInfo.getExecutionId());
//        }
//        TestDataStorageHelper.getInstance().saveResult(result, success -> {
////            if (success) loadHistory();
//        });
//        if (result.getFinished()) {
//            finishTest();
//        }
//    }

    /**
     * 通知测试平台测试完成
     * */
//    public void finishTest() {
//        clientApiRepository.finishTest(mTaskInfo);
//    }

    public void heartbeat() {
        if (!ConnectionChangeReceiver.getInstance().isWifiConnected()) {
            FileLogger.w(TAG, "heartbeat: wifi not connected");
            return;
        }
        if (vehicleInfo == null || TextUtils.isEmpty(vehicleInfo.getVIN())) {
            FileLogger.e(TAG, "heartbeat: vehicleInfo or VIN is null");
            return;
        }
        clientApiRepository.heartbeat(vehicleInfo.getVIN());
    }

    private boolean performHeartbeatSync() {
        try {
            String vin = vehicleInfo.getVIN();
            if (vin == null || vin.isEmpty()) {
                FileLogger.e(TAG, "performHeartbeat: vehicleInfo or VIN is null");
                return false;
            }

            FileLogger.i(TAG, "performHeartbeat: VIN = " + vin);
            long taskId = mTaskInfo != null ? mTaskInfo.getId() : -1;
            long executionId = mTaskInfo != null ? mTaskInfo.getExecutionId() : -1;
            HeartbeatEntity info = new HeartbeatEntity(taskId, executionId, vehicleInfo.getMode());
            VehicleApiImpl vehicleApi = new VehicleApiImpl();
            Response<ApiResponse<TestTaskBean>> response = vehicleApi.heartbeatSync(vin, info);

            if (response.isSuccessful()) {
                TestTaskBean taskInfo = response.body().getData();
                FileLogger.i(TAG, "performHeartbeat: Heartbeat successful - " + taskInfo);
                processTaskUpdate(taskInfo, "Heartbeat");
                return true;
            } else {
                FileLogger.e(TAG, "performHeartbeat: Server error - " + response.code());
                return false;
            }
        } catch (Exception e) {
            FileLogger.e(TAG, "performHeartbeat: Exception occurred - " + e.getMessage());
            mErrorCode = new ErrorHandler(App.getInstance()).handleError(e);
            Log.e(TAG, "onFailure: " + mErrorCode);
            if (mErrorCode.getCode() == ErrorCode.ERROR_API_EXECUTION_STOP.getCode()) {
                // 如果是停止测试的错误码，重置执行id，并终止当前测试任务
                mTaskInfo.setExecutionId(0);
                TestDataStorageHelper.getInstance().saveTask(mTaskInfo, success -> {});
                if (isExecutionServiceRunning()) {
                    Log.i(TAG, "performHeartbeat: Stopping TestExecutionService due to ERROR_API_EXECUTION_STOP");
                    EventBus.getDefault().post(new StopTestEvent(mTaskInfo.getExecutionId()));
                } else{
                    Log.w(TAG, "performHeartbeat: TestExecutionService is not running, no need to stop it");
                }
            }
            return false;
        }
    }

    public ErrorCode getErrorCode() {
        return mErrorCode;
    }

    public TestTaskBean getTaskInfo() {
        return mTaskInfo;
    }

    /**
     * 清理数据，释放资源
     * */
    public void clear() {
//        scheduler.shutdown();
//        clientApiRepository = null;
    }

    /**
     * 根据当前持有的任务状态，并结合一个外部传入的"手动停止的任务ID"，判断是否允许自动启动。
     * @param manuallyStoppedTaskId 用户在UI上本次会话中手动停止过的任务ID，可以为null。
     * @return 如果任务存在、需要自动启动且未被手动停止，则返回true。
     */
    public boolean isAutoStartAllowed(Long manuallyStoppedTaskId) {
        if (this.mTaskInfo == null) {
            return false;
        }
        // 规则1: 如果当前任务ID与手动停止的ID一致，则不允许自动启动。
        if (manuallyStoppedTaskId != null && manuallyStoppedTaskId.equals(this.mTaskInfo.getId())) {
            return false;
        }
        // 规则2: 最终决定权交给任务本身的标志。
        return this.mTaskInfo.isAutoStart();
    }

    /**
     * autoStart任务延迟3秒后触发自动启动
     */
    private void triggerAutoStartWithDelay() {
        scheduler.schedule(() -> {
            if (mSuiteListCallBack != null) {
                mSuiteListCallBack.suiteListSuccess();
            }
        }, 3, TimeUnit.SECONDS);
    }

    private boolean isExecutionServiceRunning() {
        return ServiceUtils.isServiceRunning(App.getInstance().getApplicationContext(), TestExecutionService.class);
    }
}
