<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#F5F5F5">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="录屏功能测试"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- 状态显示 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="状态信息"
        android:textSize="16sp"
        android:textColor="#666666"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/status_text"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="#FFFFFF"
        android:padding="12dp"
        android:text="等待操作..."
        android:textSize="14sp"
        android:textColor="#333333"
        android:gravity="top|start"
        android:scrollbars="vertical"
        android:layout_marginBottom="24dp"
        android:elevation="2dp" />

    <!-- 控制按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="24dp">

        <Button
            android:id="@+id/start_button"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="开始录制"
            android:textSize="16sp"
            android:textColor="#FFFFFF"
            android:background="#4CAF50"
            android:layout_marginBottom="12dp"
            android:elevation="4dp" />

        <Button
            android:id="@+id/stop_button"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="停止录制"
            android:textSize="16sp"
            android:textColor="#FFFFFF"
            android:background="#F44336"
            android:layout_marginBottom="12dp"
            android:elevation="4dp" />

        <Button
            android:id="@+id/diagnostic_button"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="基础诊断"
            android:textSize="16sp"
            android:textColor="#FFFFFF"
            android:background="#2196F3"
            android:layout_marginBottom="12dp"
            android:elevation="4dp" />

        <Button
            android:id="@+id/deep_diagnostic_button"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="深度诊断"
            android:textSize="16sp"
            android:textColor="#FFFFFF"
            android:background="#9C27B0"
            android:layout_marginBottom="12dp"
            android:elevation="4dp" />

        <Button
            android:id="@+id/minimal_test_button"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="最小化测试"
            android:textSize="16sp"
            android:textColor="#FFFFFF"
            android:background="#FF9800"
            android:layout_marginBottom="12dp"
            android:elevation="4dp" />

        <Button
            android:id="@+id/mediarecorder_test_button"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="MediaRecorder测试"
            android:textSize="16sp"
            android:textColor="#FFFFFF"
            android:background="#E91E63"
            android:layout_marginBottom="12dp"
            android:elevation="4dp" />

        <Button
            android:id="@+id/performance_report_test_button"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="性能报告测试"
            android:textSize="16sp"
            android:textColor="#FFFFFF"
            android:background="#607D8B"
            android:elevation="4dp" />

    </LinearLayout>

    <!-- 说明文字 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="使用说明：\n1. 点击&quot;开始录制&quot;获取录屏权限并开始录制\n2. 点击&quot;停止录制&quot;结束录制\n3. 点击&quot;运行诊断&quot;检查录屏环境\n4. 录制文件保存在 /storage/emulated/0/XPAutoTest/video/ 目录"
        android:textSize="12sp"
        android:textColor="#888888"
        android:lineSpacingExtra="4dp"
        android:background="#FFFFFF"
        android:padding="12dp"
        android:elevation="2dp" />

    <!-- 测试内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="#FFFFFF"
            android:padding="16dp"
            android:elevation="2dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="测试内容区域"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="16dp" />

            <!-- 彩色方块用于测试录屏效果 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <View
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_weight="1"
                    android:background="#FF5722"
                    android:layout_marginEnd="4dp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_weight="1"
                    android:background="#4CAF50"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_weight="1"
                    android:background="#2196F3"
                    android:layout_marginStart="4dp" />

            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="这是一个测试文本，用于验证录屏是否能正确捕获屏幕内容。如果录屏正常工作，这些文字和上面的彩色方块都应该在录制的视频中可见。"
                android:textSize="14sp"
                android:textColor="#666666"
                android:lineSpacingExtra="4dp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
