<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    
    <!-- 屏幕边框 -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#666666"
        android:strokeWidth="2"
        android:pathData="M3,4 L21,4 L21,16 L3,16 Z" />
        
    <!-- 屏幕底座 -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#666666"
        android:strokeWidth="2"
        android:pathData="M8,16 L8,18 L16,18 L16,16" />
        
    <!-- 录制圆点 -->
    <path
        android:fillColor="#FF4444"
        android:pathData="M12,10m-3,0a3,3 0,1 1,6 0a3,3 0,1 1,-6 0" />
        
</vector>
