package com.xiaopeng.xpautotest.ui;

import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.IBinder;
import android.os.Parcelable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import com.xiaopeng.lib.utils.ThreadUtils;
import com.xiaopeng.screen.ScreenDevice;
import com.xiaopeng.screen.ScreenTaskManagerFactory;
import com.xiaopeng.xpautotest.App;
import com.xiaopeng.xpautotest.BuildConfig;
import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.carmanager.CarClientWrapper;
import com.xiaopeng.xpautotest.carmanager.carcontroller.IBcmController;
import com.xiaopeng.xpautotest.carmanager.carcontroller.IMcuController;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.bean.TestScriptEntity;
import com.xiaopeng.xpautotest.constant.EnvironmentConfig;
import com.xiaopeng.xpautotest.helper.DialogHelper;
import com.xiaopeng.xpautotest.helper.TestDataStorageHelper;
import com.xiaopeng.xpautotest.model.ITestSuiteItem;
import com.xiaopeng.xpautotest.model.TestCaseItem;
import com.xiaopeng.xpautotest.service.DebuggingModeService;
import com.xiaopeng.xpautotest.service.TestExecutionService;
import com.xiaopeng.xpautotest.utils.ApiRouterUtils;
import com.xiaopeng.xpautotest.utils.SystemPropertiesUtils;
import com.xiaopeng.xpautotest.viewmodel.SuiteViewModel;
import com.xiaopeng.xui.app.XDialogInterface;
import com.xiaopeng.xui.widget.XButton;
import com.xiaopeng.xui.widget.XImageButton;
import com.xiaopeng.xui.app.XActivity;
import com.xiaopeng.xui.widget.XTextView;

import java.util.ArrayList;
import java.util.List;

import androidx.lifecycle.ViewModelProvider;

import static com.xiaopeng.xui.Xui.getContext;

public class MainActivity extends XActivity {

    private static final String TAG = "MainActivity";

    private XTextView mTitleTextView;
    private XTextView mTitleVinTextView;
    private XTextView mTitleCfcTextView;
    private XTextView mTitleAppVersionTextView;
    private XTextView mTitleTaskNameTextView;
    private XTextView mTitleStationTextView;
    private XButton mReloadTaskButton;
    private XImageButton mTitleCloseButton;
    private ViewGroup mLoadingContainer;
    private ViewGroup mErrorContainer;
    private XTextView mErrorTextView;

    private SuiteViewModel viewModel;
    private TestExecutionService service;
    private boolean isBound = false;
    private IMcuController mIMcuController;

    private final ServiceConnection connection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName className, IBinder service) {
            isBound = true;
            TestExecutionService.LocalBinder binder = (TestExecutionService.LocalBinder) service;
            MainActivity.this.service = binder.getService();
//            bindServiceObservers();
            Log.d(TAG, "onServiceConnected: " + MainActivity.this.service);
        }

        @Override
        public void onServiceDisconnected(ComponentName arg0) {
            service = null;
            isBound = false;
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.at_activity_factory);

        initViews();

        // 初始化服务端地址
        initEnvironment();

        // 初始化ViewModel
        viewModel = new ViewModelProvider(this).get(SuiteViewModel.class);

        showLoading(true);
        // 在目标Activity的onCreate中
        Bundle extras = getIntent().getExtras();
        String tag;
        if (extras != null) {
            // 任务执行过后返回这里
            tag = extras.getString(Constant.EXTRA_TEST_DATA_LOAD_TAG, "remote"); // 此时为 "local"
            long suiteId = extras.getLong(Constant.EXTRA_TEST_SUITE_ID, 0);
            long manuallyStopTaskId = extras.getLong(Constant.EXTRA_MANUAL_STOP_TASK_ID, -1L);
            if (manuallyStopTaskId > 0) {
                viewModel.setManuallyStoppedTaskId(manuallyStopTaskId);
            }
            viewModel.setCurrentSuiteId(suiteId);
            viewModel.setAwaitingPostExecutionHeartbeat(true);
            viewModel.load(tag);
        } else {
            //  正常APK启动
            tag = "remote";
            viewModel.load(tag);
        }

        viewModel.getTaskInfo().observe(this, taskInfo -> {
            String taskNameFormat = getString(R.string.factory_title_script_date);
            String taskProgressFormat = getString(R.string.factory_title_station);
            if (taskInfo != null) {
                mTitleTaskNameTextView.setText(String.format(taskNameFormat, taskInfo.getName()));
                mTitleStationTextView.setText(String.format(taskProgressFormat, taskInfo.getProgress()));
            } else {
                mTitleTaskNameTextView.setText(String.format(taskNameFormat, "--"));
                mTitleStationTextView.setText(String.format(taskProgressFormat, "--"));
            }
        });
        viewModel.getLoading().observe(this, this::showLoading);
        viewModel.getErrorCode().observe(this, errorCode -> {
            if (errorCode != null) {
                mErrorTextView.setText(errorCode.getMessageRes());
                mErrorContainer.setVisibility(View.VISIBLE);
            } else {
                mErrorContainer.setVisibility(View.GONE);
            }
        });

        initTitleView();

        // 绑定测试服务
//        bindService(new Intent(this, TestExecutionService.class), connection, BIND_AUTO_CREATE);

        if (findViewById(R.id.fragment_container_left) == null ||
                findViewById(R.id.fragment_container_right) == null) {
            throw new RuntimeException("检查 activity_main.xml 中的容器ID是否正确");
        }

        // 加载Fragment
        Log.i("MainActivity", "onCreate: " + savedInstanceState);
        if (savedInstanceState == null) {
            Log.i("MainActivity", "load fragments");
            getSupportFragmentManager().beginTransaction()
                    .add(R.id.fragment_container_left, new TestSuiteListFragment())
                    .add(R.id.fragment_container_right, new TestCaseListFragment())
                    .commit();
        }
    }

    private void initViews() {
        mTitleTextView = findViewById(R.id.tv_title_vatc_autotest);
        mTitleVinTextView = findViewById(R.id.tv_title_vin);
        mTitleCfcTextView = findViewById(R.id.tv_title_cfc);
        mTitleAppVersionTextView = findViewById(R.id.tv_title_app_version);
        mTitleStationTextView = findViewById(R.id.tv_title_station);
        mTitleTaskNameTextView = findViewById(R.id.tv_title_script_date);

        mLoadingContainer = findViewById(R.id.web_loading_container);
        mErrorContainer = findViewById(R.id.web_error_container);
        mErrorTextView = findViewById(R.id.tv_error_message);

        mErrorContainer.setVisibility(View.GONE);
    }

    private void initTitleView() {
        mTitleCloseButton = findViewById(R.id.ib_title_close);
        if (mTitleCloseButton != null) {
            mTitleCloseButton.setImageResource(com.xiaopeng.xpui.R.drawable.x_ic_small_close);
            mTitleCloseButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    finishMyself();
                }
            });
        }

        if (ApiRouterUtils.isFactoryMode()) {
            mTitleTextView.setText(R.string.autotest_title_factory);
        } else {
            mTitleTextView.setText(R.string.autotest_title);
        }

        mTitleVinTextView.setText(SystemPropertiesUtils.getVIN());
        mTitleCfcTextView.setText(SystemPropertiesUtils.getSoftwareVersionCode());

        String taskNameFormat = getString(R.string.factory_title_script_date);
        String taskProgressFormat = getString(R.string.factory_title_station);
        mTitleTaskNameTextView.setText(String.format(taskNameFormat, "--"));
        mTitleStationTextView.setText(String.format(taskProgressFormat, "--"));

        setAppVersion();

        setTitleViewText();

        // 点击刷新会重新加载任务
        mReloadTaskButton = findViewById(R.id.btn_reload_task);
        mReloadTaskButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showReloadTaskDialog();
            }
        });
    }

    private void setTitleViewText() {
//        if (mTitleVinTextView == null) {
//            return;
//        }


        String taskNameFormat = getString(R.string.factory_title_script_date);
        String taskName = "工厂测试任务";
        if (TextUtils.isEmpty(taskName)) {
            taskName = getString(R.string.unknown);
        }

//        if (getAccountClient() == null || !getAccountClient().isSessionAvailable()) {
//            LogUtils.i(TAG, "Session is unavailable");
//            return;
//        }
//
//        setVinText();
//
//        AppInfoEntity appInfo = getAccountClient().getAppInfo();
//        if (appInfo != null) {
//            String appVersionFormat = ResourceUtils.getString(getApplicationContext(), R.string.factory_title_app_version);
//            String versionName = appInfo.getVersionName();
//            mTitleAppVersionTextView.setText(String.format(appVersionFormat, versionName));
//
//            String scriptDateFormat = ResourceUtils.getString(getApplicationContext(), R.string.factory_title_script_date);
//            String scriptDate = appInfo.getScriptDate();
//            if (TextUtils.isEmpty(scriptDate)) {
//                scriptDate = ResourceUtils.getString(getApplicationContext(), R.string.unknown);
//            }
//            mTitleScriptDateTextView.setText(String.format(scriptDateFormat, scriptDate));
//        } else {
//            Log.i(TAG, "App info is null");
//        }

    }

    // 设置应用版本号
    private void setAppVersion() {
        try {
            PackageInfo pInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
            String appVersionFormat = getString(R.string.factory_title_app_version);
            String version = String.format(appVersionFormat, pInfo.versionName);
            mTitleAppVersionTextView.setText(version);
        } catch (PackageManager.NameNotFoundException e) {
            mTitleAppVersionTextView.setText("Version Unknown");
        }
    }

//    private boolean isFactoryMode() {
//        if (CarClientWrapper.getInstance().isCarServiceConnected()) {
//            Log.d(TAG, "Car service connected");
//            mIMcuController = (IMcuController) CarClientWrapper.getInstance().getController(CarClientWrapper.XP_MCU_SERVICE);
//            return mIMcuController != null && mIMcuController.getFactoryModeSwitchStatus() == 0;
//        }
//        return false;
//    }

    private void initEnvironment() {
        EnvironmentConfig.getInstance().setEnvironment(ApiRouterUtils.isFactoryMode(), BuildConfig.ENV_TYPE);
    }

    public void finishMyself() {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "finishMyself:" + getClass().getSimpleName());
        }

        if (!isFinishing()) {
            stopService(new Intent(this, TestExecutionService.class));
            finish();
        }
    }

//    private void bindServiceObservers() {
//        if (service != null) {
//            service.getResultLiveData().observe(this, result -> {
//                viewModel.updateResult(result);
//            });
//        }
//    }

    // 开始测试
    public void startAllTesting(List<ITestSuiteItem> suiteItemList, Long executionId) {
        Log.i(TAG, "startAllTesting: executionId=" + executionId);
        List<TestScriptEntity> testScripts = new ArrayList<>();
        if (suiteItemList == null) {
            Log.w(TAG, "startAllTesting: suiteItemList is null");
            return;
        }
        for (ITestSuiteItem suite : suiteItemList) {
            if (suite != null && suite.getItems() != null) {
                for (TestCaseItem testCase : suite.getItems()) {
                    TestScriptEntity script = new TestScriptEntity(testCase.getScriptId(), testCase.getSteps());
                    testScripts.add(script);
                }
            }
        }
        startTestExecution(testScripts, executionId, TestExecutionService.MODE_ALL, 0);
    }

    public void startSuiteTesting(ITestSuiteItem suite, Long executionId) {
        Log.i(TAG, "startSuiteTesting: suite=" + suite.getName() + ", executionId=" + executionId);
        startTesting(suite.getItems(), executionId, TestExecutionService.MODE_SUITE, suite.getId());
    }

    public void startCaseTesting(TestCaseItem testCase, Long executionId) {
        List<TestCaseItem> testCases = new ArrayList<>();
        testCases.add(testCase);
        startTesting(testCases, executionId, TestExecutionService.MODE_RETRY, testCase.getSuiteId());
    }

    public void startFailedCaseTesting(List<TestCaseItem> failedCases, Long executionId, long suiteId) {
        Log.i(TAG, "startFailedCaseTesting: failedCases count=" + failedCases.size() + ", executionId=" + executionId + ", suiteId=" + suiteId);
        startTesting(failedCases, executionId, TestExecutionService.MODE_RETRY, suiteId);
    }

    public void startTesting(List<TestCaseItem> testCases, Long executionId, String mode, long suiteId) {
        if (testCases == null || testCases.isEmpty()) {
            Log.w(TAG, "startTesting: testCases is null or empty, executionId=" + executionId + ", mode=" + mode);
            return;
        }
        List<TestScriptEntity> testScripts = new ArrayList<>();
        for (TestCaseItem testCase : testCases) {
            TestScriptEntity script = new TestScriptEntity(testCase.getScriptId(), testCase.getSteps());
            testScripts.add(script);
        }
        startTestExecution(testScripts, executionId, mode, suiteId);
    }

    private void startTestExecution(List<TestScriptEntity> testScripts, Long executionId, String mode, long suiteId) {
        if(testScripts == null || testScripts.isEmpty()) {
            Log.w(TAG, "startTestExecution: testScripts is null or empty, executionId=" + executionId + ", mode=" + mode);
            return;
        }
        Log.i(TAG, "startTestExecution: script count=" + testScripts.size());
        Intent intent = new Intent(this, TestExecutionService.class);
        intent.putParcelableArrayListExtra(Constant.EXTRA_TEST_SCRIPTS, (ArrayList<? extends Parcelable>) testScripts);
        intent.putExtra(Constant.EXTRA_EXECUTION_ID, executionId);
        intent.putExtra(Constant.EXTRA_TEST_SUITE_ID, suiteId);
        intent.putExtra(Constant.EXTRA_TEST_MODE, mode);
//        ContextCompat.startForegroundService(this, intent);
        startService(intent);
    }

    // 停止测试
    public void stopTesting() {
        if (isBound && service != null) {
            unbindService(connection);
            isBound = false;
        }
        stopService(new Intent(this, TestExecutionService.class));
    }

    public void startDebugModeService() {
        Log.i(TAG, "startDebugModeService: ");
        Intent intent = new Intent(this, DebuggingModeService.class);
        startService(intent);
    }

    public void showLoading(boolean isShow) {
        Log.d(TAG, "showLoading: " + isShow);
        if (mLoadingContainer != null) {
            mLoadingContainer.setVisibility(isShow ? View.VISIBLE : View.GONE);
        }
    }

    private Runnable mLoadingHideRunnable = new Runnable() {
        @Override
        public void run() {
            showLoading(false);
        }
    };

    private void showReloadTaskDialog() {
        // 显示关闭对话框
        String title = getContext().getString(R.string.reload_task_confirm_title);
        String message = getContext().getString(R.string.reload_task_confirm_message);
        String positiveButtonText = getContext().getString(R.string.reload_task_confirm_positive_button);
        String negativeButtonText = getContext().getString(R.string.reload_task_confirm_negative_button);

        DialogHelper.getInstance().showDialog(getContext(), title, message,
                positiveButtonText, negativeButtonText, (xDialog, buttonId) -> {
                    xDialog.dismiss();
                    switch (buttonId) {
                        case XDialogInterface.BUTTON_POSITIVE:
                            // reload task and scripts
                            TestDataStorageHelper.getInstance().clearHistory(()->{
                                Log.i(TAG, "Clear history done");
                                showLoading(true);
                                // 刷新任务的时候，清除“手动停止的任务ID” 标识
                                viewModel.setManuallyStoppedTaskId(-1L);
                                viewModel.reload("force_remote");
                            });
                            break;

                        case XDialogInterface.BUTTON_NEGATIVE:
                            break;
                    }
                });
    }

    public boolean isGearP() {
        int screenId = ScreenTaskManagerFactory.get().getScreenId(App.getInstance().getPackageName());
        if (ScreenDevice.SCREEN_RSE == screenId || ScreenDevice.SCREEN_PSG == screenId) {
            return true;
        }
//        return mIVcuController != null && mIVcuController.isPGear();
        return true;
    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy, isBound:" + isBound);
        super.onDestroy();
        if (isBound) {
            unbindService(connection);
            isBound = false;
        }
        if (service != null) {
            stopService(new Intent(this, TestExecutionService.class));
            service = null;
        }

        ThreadUtils.removeRunnable(mLoadingHideRunnable);
        mLoadingHideRunnable = null;
    }
}
