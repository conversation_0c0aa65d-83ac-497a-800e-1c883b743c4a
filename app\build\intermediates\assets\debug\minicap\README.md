# Minicap二进制文件目录

## 目录结构

此目录包含不同架构的minicap二进制文件，用于屏幕录制功能。

```
minicap/
├── arm64-v8a/
│   ├── minicap          # ARM64架构主程序
│   └── minicap.so       # ARM64架构共享库
├── armeabi-v7a/
│   ├── minicap          # ARMv7架构主程序
│   └── minicap.so       # ARMv7架构共享库
├── x86/
│   ├── minicap          # x86架构主程序
│   └── minicap.so       # x86架构共享库
├── x86_64/
│   ├── minicap          # x86_64架构主程序
│   └── minicap.so       # x86_64架构共享库
└── README.md            # 本说明文件
```

## 文件获取

### 方法1: 从STF项目下载预编译版本

1. 访问 STF releases 页面: https://github.com/openstf/stf/releases
2. 下载对应版本的minicap文件
3. 解压并按架构分类放置

### 方法2: 自行编译

```bash
# 克隆STF项目
git clone https://github.com/openstf/stf.git
cd stf

# 编译minicap (需要Android NDK)
cd vendor/minicap
ndk-build

# 编译结果在libs/目录下
```

### 方法3: 从设备提取 (如果已安装STF)

```bash
# 连接设备
adb connect <device_ip>

# 提取minicap文件
adb pull /data/local/tmp/minicap ./
adb pull /data/local/tmp/minicap.so ./
```

## 文件验证

确保文件完整性和权限：

```bash
# 检查文件是否存在
ls -la arm64-v8a/
ls -la armeabi-v7a/
ls -la x86/
ls -la x86_64/

# 验证文件类型
file arm64-v8a/minicap
file arm64-v8a/minicap.so

# 设置权限 (如果需要)
chmod 755 */minicap
chmod 644 */minicap.so
```

## 支持的架构

- **arm64-v8a**: 64位ARM架构 (主流Android设备)
- **armeabi-v7a**: 32位ARM架构 (较老的Android设备)
- **x86**: 32位x86架构 (模拟器)
- **x86_64**: 64位x86架构 (模拟器)

## 使用说明

应用会根据设备架构自动选择对应的minicap文件：

1. 检测设备ABI (`Build.SUPPORTED_ABIS[0]`)
2. 从assets复制对应架构的文件到 `/data/local/tmp/`
3. 设置可执行权限
4. 启动minicap进程

## 注意事项

1. **文件大小**: 每个minicap约1-2MB，minicap.so约500KB-1MB
2. **权限要求**: 需要系统级权限才能在/data/local/tmp/执行
3. **兼容性**: 不同Android版本可能需要不同版本的minicap
4. **更新**: 定期检查STF项目更新，获取最新版本

## 故障排除

### 文件缺失
如果某个架构的文件缺失，应用会回退到arm64-v8a版本。

### 权限问题
确保应用具有系统签名和相应权限：
```xml
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### 版本不兼容
如果minicap无法在目标设备上运行，尝试：
1. 更新到最新版本的minicap
2. 检查设备的Android版本兼容性
3. 查看logcat错误信息

## 许可证

minicap遵循Apache 2.0许可证，详见STF项目。

## 更多信息

- STF项目: https://github.com/openstf/stf
- Minicap文档: https://github.com/openstf/minicap
- Android ABI指南: https://developer.android.com/ndk/guides/abis
