package com.xiaopeng.xpautotest.accessibility;

import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.GestureDescription;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.Rect;
import android.os.Build;
import android.os.Bundle;
import android.view.Display;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.view.accessibility.AccessibilityWindowInfo;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.lib.utils.ThreadUtils;
import com.xiaopeng.screen.ScreenTaskManagerFactory;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.utils.CMDUtils;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.system.ScreenCapture;
import com.xiaopeng.xpautotest.community.config.GlobalConfig;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Stack;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import com.xiaopeng.xpautotest.community.event.FileUploadEvent;
import org.greenrobot.eventbus.EventBus;
import android.util.Xml;
import org.xmlpull.v1.XmlSerializer;
import java.util.Set;
import java.util.HashSet;
import com.xiaopeng.executor.action.accessibility.GestureCallback;

public class BaseAccessibilityService extends AccessibilityService {
    private static final String TAG = "BaseAccessibilityService";

    private static final int GESTURE_DURATION = 300;
    private static final int MAX_TRAVERSAL_DEPTH = 50;
    private Context mContext;
    //    private AccessibilityManager mAccessibilityManager;
    private static BaseAccessibilityService mInstance;
    private String currentActivityName = "";
    private String currentToastText = "";
    private List<String> blackActivityNames = Constant.BLACK_ACTIVITY_NAMES;


    public void init(Context context) {
        mContext = context.getApplicationContext();
    }

    public static synchronized BaseAccessibilityService getInstance() {
        if (mInstance == null) {
            mInstance = new BaseAccessibilityService();
        }
        return mInstance;
    }

    @Override
    protected void onServiceConnected() {
        super.onServiceConnected();
        blackActivityNames = GlobalConfig.getInstance().getBlackActivityNames();
        FileLogger.i(TAG, "blackActivityNames: " + blackActivityNames.toString());
    }

//    /**
//     * Check当前辅助服务是否启用
//     *
//     * @param serviceName serviceName
//     * @return 是否启用
//     */
//    public boolean checkAccessibilityEnabled(String serviceName) {
//        List<AccessibilityServiceInfo> accessibilityServices =
//                mAccessibilityManager.getInstalledAccessibilityServiceList();
//        //mAccessibilityManager.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK);
//        FileLogger.d(TAG, "accessibilityServices:" + accessibilityServices);
//        for (AccessibilityServiceInfo info : accessibilityServices) {
//            FileLogger.d(TAG, "info:" + info);
//            if (info.getId().equals(serviceName)) {
//                return true;
//            }
//        }
//        return false;
//    }

    public String getCurrentActivity() {
        if (currentActivityName != null && !currentActivityName.isEmpty()) {
            FileLogger.i(TAG, "use event cache current activity: " + currentActivityName);
            return currentActivityName;
        } else {
            // getClassName()返回的不一定是Activity的类名，可能是View、Dialog的类名
            AccessibilityNodeInfo nodeInfo = getRootInActiveWindowWithRetry();
            if (nodeInfo == null) {
                return null;
            } else {
                String packageName = nodeInfo.getPackageName().toString();

                String className = nodeInfo.getClassName().toString();

                nodeInfo.recycle();

                FileLogger.d("CurrentActivity", packageName + "/" + className);
                return packageName + "/" + className;
            }
        }
    }

    /**
     * 获取当前的Toast文本
     *
     * @return 当前的Toast文本
     */
    public String getCurrentToast() {
        return currentToastText;
    }

    /**
     * 模拟点击事件,如果该node不能点击，则点击父node，将点击事件一直向父级传递，直至到根node或者找到一个可以点击的node
     *
     * @param nodeInfo nodeInfo
     */
    public void performViewClick(AccessibilityNodeInfo nodeInfo) {
        if (nodeInfo == null) {
            return;
        }
        while (nodeInfo != null) {
            if (nodeInfo.isClickable()) {
                FileLogger.i(TAG, "performClick:");
                nodeInfo.performAction(AccessibilityNodeInfo.ACTION_CLICK);
                break;
            }
            nodeInfo = nodeInfo.getParent();
        }
    }

    /**
     * 返回HOME键
     */
    public boolean performHomeClick() {
        currentActivityName = "";
        FileLogger.i(TAG, "performHomeClick. Resetting currentActivityName.");
        return performGlobalAction(GLOBAL_ACTION_HOME);
    }


    /**
     * 手势操作，因为path不能小于0，因此小于则直接返回，不操作，另外如果有需求，可以自行修改小于则设置为0或者屏幕的宽高
     *
     * @param moveToX
     * @param moveToY
     * @param lineToX
     * @param lineToY
     * @param startTime
     * @param duration
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public void gesture(int moveToX, int moveToY, int lineToX, int lineToY, long startTime,
                        long duration, String mAction, String nextAction, AccessibilityNodeInfo info) {
        try {
            if (moveToX < 0 || moveToY < 0 || lineToX < 0 || lineToY < 0) {
                FileLogger.e(TAG, "path nagative");
                FailureContextHolder.setFailureIfNotSet(FailureCode.SI001);
                return;
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                FileLogger.d(TAG, "gesture action: " + mAction + " nextAction: " + nextAction
                        + "moveToX: " + moveToX + " moveToY: " + moveToY
                        + " lineToX: " + lineToX + " lineToY: " + lineToY);
                FileLogger.d(TAG, "gesture node: " + info);
                Path path = new Path();
                path.moveTo(moveToX, moveToY);
                path.lineTo(lineToX, lineToY);
                GestureDescription.StrokeDescription strokeDescription =
                        new GestureDescription.StrokeDescription(path, startTime, duration, false);
                int sdkVersion = Build.VERSION.SDK_INT;
                final int R = 30;
                GestureDescription gestureDescription;
                CharSequence packageName = info.getPackageName();
                int displayId = ScreenTaskManagerFactory.get().getScreenId(packageName.toString());
                if (sdkVersion < R) {
                    //  adb shell input -d swap sx sy ex ey system权限
                    ThreadUtils.execute(() -> {
                        String commandTemplate = "input -d %d swipe %d %d %d %d";
                        String command = String.format(commandTemplate, displayId, moveToX, moveToY, lineToX, lineToY);
                        FileLogger.d(TAG, "gesture use command: " + command);
                        CMDUtils.CMD_Result result = CMDUtils.runCMD(command, true, true);
                        FileLogger.d(TAG, "result:" + result.resultCode + ", "+ result.error + ", " + result.success);
                        executeNextAction(info, mAction, nextAction);
                    });
                } else {
                    // 根据AccessibilityNodeInfo查询ScreenId or DisplayId
                    // 只有android R开始才支持分屏派发gesture
                    List<GestureDescription.StrokeDescription> strokes = new ArrayList<>();
                    strokes.add(strokeDescription);
                    gestureDescription = createGestureDescriptionByReflection(strokes, displayId);
                    if (gestureDescription == null) {
                        FileLogger.e(TAG, "Dispatch gesture failed, because gesture description is null.");
                        return;
                    }
                    dispatchGesture(gestureDescription, new GestureResultCallback() {
                        @Override
                        public void onCompleted(GestureDescription gestureDescription) {
                            super.onCompleted(gestureDescription);
                            path.close();
                            FileLogger.d(TAG, "onCompleted");
                            ThreadUtils.runOnMainThreadDelay(() ->
                                    executeNextAction(info, mAction, nextAction),GESTURE_DURATION);
                        }
                        @Override
                        public void onCancelled(GestureDescription gestureDescription) {
                            path.close();
                            FileLogger.d(TAG, "onCancelled");
                        }
                    }, null);
                }
            }
        } catch (IllegalArgumentException e) {
            FileLogger.e(TAG, "gesture exception message: " + e.getMessage());
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    private void executeNextAction(AccessibilityNodeInfo info, String action, String nextAction) {
        if (Constant.EVENT_VALUE_DIRECTION_LEFT.equals(action)
                && Constant.EVENT_VALUE_DIRECTION_UP.equals(nextAction)) {
            performScrollUp(info, null);
        } else if (Constant.EVENT_VALUE_DIRECTION_RIGHT.equals(action)
                && "down".equals(nextAction)) {
            performScrollDown(info, null);
        }
    }

    private GestureDescription createGestureDescriptionByReflection(List<GestureDescription.StrokeDescription> stokes, int displayId){
        try {
            Class<?> clazz = Class.forName("android.accessibilityservice.GestureDescription");
            Constructor<?> constructor = clazz.getDeclaredConstructor(List.class, int.class);
            return (GestureDescription) constructor.newInstance(stokes, displayId);
        } catch (ClassNotFoundException | NoSuchMethodException | InvocationTargetException |
                 IllegalAccessException | InstantiationException e) {
            FileLogger.e(TAG, "makeGestureDescription", e);
        }
        return null;
    }
    /**
     * 获取根节点，带重试机制
     * @return 根节点，如果不支持则返回null
     */
    private AccessibilityNodeInfo getRootInActiveWindowWithRetry() {
        final int MAX_RETRIES = 2;
        final long RETRY_INTERVAL_MS = 300;
        // 默认屏幕或支持多屏的Android版本，使用重试机制
        AccessibilityNodeInfo nodeInfo = null;
        for (int i = 0; i < MAX_RETRIES; i++) {

            // 这个是可以在所有屏幕上找到当前活动窗口的根节点
            nodeInfo = getRootInActiveWindow();
            //nodeInfo = (displayId == 0) ? getRootInActiveWindow() : getRootInActiveWindow(displayId);
            if (nodeInfo != null) {
                return nodeInfo;
            }
            if (i < MAX_RETRIES - 1) {
                FileLogger.w(TAG, "getRootInActiveWindowWithRetry: Attempt " + (i + 1) + " failed, retrying...");
                sleep(RETRY_INTERVAL_MS);
            }
        }
        FailureContextHolder.setFailureIfNotSet(FailureCode.EI008);
        FileLogger.e(TAG, "getRootInActiveWindowWithRetry: Failed to get root node after " + MAX_RETRIES + " attempts.");
        return null;
    }

    /**
     * 查找对应文本的View
     *
     * @param text      text
     * @param onlyClickable 该View是否可以点击
     * @param displayId 显示屏ID，0表示默认屏幕
     * @return View
     */
    public AccessibilityNodeInfo findViewByText(String text, boolean onlyClickable, int displayId) {
        AccessibilityNodeInfo accessibilityNodeInfo = getRootInActiveWindowWithRetry();

        if (accessibilityNodeInfo == null) {
            FileLogger.e(TAG, "getRootInActiveWindow is null for displayId: " + displayId);
            return null;
        }

        List<AccessibilityNodeInfo> nodeInfoList = accessibilityNodeInfo.findAccessibilityNodeInfosByText(text);
        if (nodeInfoList != null && !nodeInfoList.isEmpty()) {
            for (AccessibilityNodeInfo nodeInfo : nodeInfoList) {
                if (nodeInfo != null && nodeInfo.getText() != null
                        && text.equals(nodeInfo.getText().toString())
                        && (!onlyClickable || nodeInfo.isClickable())) {
                    FileLogger.i(TAG, "findViewByText: found '" + text + "' on displayId: " + displayId);
                    return nodeInfo;
                }
            }
        }
        FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
        return null;
    }

    /**
     * 查找指定屏幕上对应文本的View列表，返回所有文本完全匹配的View
     *
     * @param text text
     * @param displayId 屏幕ID
     * @return View列表
     */
    public List<AccessibilityNodeInfo> findViewsByText(String text, int displayId) {
        AccessibilityNodeInfo accessibilityNodeInfo = getRootInActiveWindowWithRetry();
        if (accessibilityNodeInfo == null) {
            FileLogger.e(TAG, "getRootInActiveWindow is null for displayId: " + displayId);
            return null;
        }
        List<AccessibilityNodeInfo> nodes = accessibilityNodeInfo.findAccessibilityNodeInfosByText(text);

        if (nodes == null || nodes.isEmpty()) {
            FileLogger.i(TAG, "findViewsByText: No nodes found for text: " + text);
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            try {
                UIHierarchyDumper.printNodeHierarchy(accessibilityNodeInfo);
            } catch (Exception e) {
                FileLogger.e(TAG, "printNodeHierarchy failed: " + e.getMessage(), e);
            }
            accessibilityNodeInfo.recycle();
            return nodes; 
        }
        accessibilityNodeInfo.recycle();
        List<AccessibilityNodeInfo> exactMatchNodes = new ArrayList<>();
        for (AccessibilityNodeInfo node : nodes) {
            if (node != null && node.getText() != null && text.equals(node.getText().toString())) {
                exactMatchNodes.add(node);
            } else if (node != null) {
                node.recycle();
            }
        }

        return exactMatchNodes;
    }

    /**
     * 查找包含对应文本的View
     *
     * @param text      text
     * @param onlyClickable 该View是否可以点击
     * @param displayId 显示屏ID，0表示默认屏幕
     * @return View
     */
    public AccessibilityNodeInfo findViewByTextContains(String text, boolean onlyClickable, int displayId) {
        List<AccessibilityNodeInfo> accessibilityNodeInfos = new ArrayList<>();
        Stack<AccessibilityNodeInfo> nodeStack = new Stack<>();

        AccessibilityNodeInfo node = getRootInActiveWindowWithRetry();

        if (node == null) {
            FileLogger.e(TAG, "getRootInActiveWindow is null for displayId: " + displayId);
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            return null;
        }

        nodeStack.add(node);
        while (!nodeStack.isEmpty()) {
            node = nodeStack.pop();
            if (node != null && node.getText() != null && node.getText().toString().contains(text)
                    && (!onlyClickable || node.isClickable())) {
                accessibilityNodeInfos.add(node);
            }
            if (node == null || node.getChildCount() == 0) {
                continue;
            }
            //获得节点的子节点，对于二叉树就是获得节点的左子结点和右子节点
            int childCount = node.getChildCount();
            for (int i = 0; i < childCount; i++) {
                AccessibilityNodeInfo child = node.getChild(i);
                if (child != null) {
                    nodeStack.push(child);
                }
            }
        }
        if (!accessibilityNodeInfos.isEmpty()) {
            FileLogger.i(TAG, "findViewByTextContains: found '" + text + "' on displayId: " + displayId);
            return accessibilityNodeInfos.get(0);
        } else {
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            return null;
        }
    }
    /**
     * 查找对应ID的View
     *
     * @param id id
     * @param displayId 显示屏ID，0表示默认屏幕
     * @return View
     */
    public AccessibilityNodeInfo findViewByID(String id, int displayId) {
        AccessibilityNodeInfo accessibilityNodeInfo = getRootInActiveWindowWithRetry();

        if (accessibilityNodeInfo == null) {
            FileLogger.e(TAG, "getRootInActiveWindow is null for displayId: " + displayId);
            return null;
        }

        List<AccessibilityNodeInfo> nodeInfoList = accessibilityNodeInfo.findAccessibilityNodeInfosByViewId(id);
        if (nodeInfoList != null && !nodeInfoList.isEmpty()) {
            FileLogger.i(TAG, "findViewByID: " + nodeInfoList.size());
            FileLogger.i(TAG, "findViewByID: found '" + id + "' on displayId: " + displayId+
                    ", node count: " + nodeInfoList.size());
            for (AccessibilityNodeInfo nodeInfo : nodeInfoList) {
                if (nodeInfo != null) {
                    FileLogger.i(TAG, "findViewByID: " + nodeInfo.toString());
                    return nodeInfo;
                }
            }
        }
        FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
        return null;
    }

    /**
     * 点击对应文本的一个view，前提是这个view能够点击，即 clickable == true，
     *
     * @param text 要查找的文本
     */
    public void clickViewByText(String text) {
        AccessibilityNodeInfo accessibilityNodeInfo = getRootInActiveWindow();
        if (accessibilityNodeInfo == null) {
            return;
        }
        List<AccessibilityNodeInfo> nodeInfoList = accessibilityNodeInfo.findAccessibilityNodeInfosByText(text);
        if (nodeInfoList != null && !nodeInfoList.isEmpty()) {
            for (AccessibilityNodeInfo nodeInfo : nodeInfoList) {
                if (nodeInfo != null) {
                    performViewClick(nodeInfo);
                    break;
                }
            }
        }
    }

    /**
     * 点击对应id的一个view，前提是这个view能够点击，即 clickable == true，
     *
     * @param id 要查找的id
     */
    public void clickViewByID(String id) {
        AccessibilityNodeInfo accessibilityNodeInfo = getRootInActiveWindow();
        if (accessibilityNodeInfo == null) {
            return;
        }
        List<AccessibilityNodeInfo> nodeInfoList = accessibilityNodeInfo.findAccessibilityNodeInfosByViewId(id);
        if (nodeInfoList != null && !nodeInfoList.isEmpty()) {
            for (AccessibilityNodeInfo nodeInfo : nodeInfoList) {
                if (nodeInfo != null) {
                    performViewClick(nodeInfo);
                    break;
                }
            }
        }
    }

    /**
     * 递归遍历node及其子node，点击文本相同的节点，全点击
     *
     * @param text
     * @param parentNode
     */
    public void clickNodesByText(String text, AccessibilityNodeInfo parentNode) {
        if (parentNode == null) {
            return;
        }
        int childCount = parentNode.getChildCount();
        if (childCount == 0) {  //叶节点
            if (parentNode.getText() == null) {
                return;
            }
            if (!text.equals(parentNode.getText().toString())) {
                return;
            }
            Rect rect = new Rect();
            parentNode.getBoundsInScreen(rect);
            int moveToX = (rect.left + rect.right) / 2;
            int moveToY = (rect.top + rect.bottom) / 2;
            int lineToX = (rect.left + rect.right) / 2;
            int lineToY = (rect.top + rect.bottom) / 2;
            //gesture(moveToX, moveToY, lineToX, lineToY, 100L, 400L);
            return;
        }
        for (int i = 0; i < childCount; i++) {
            AccessibilityNodeInfo child = parentNode.getChild(i);
            clickNodesByText(text, child);
        }
    }

    /**
     * 根据文本查找指定屏幕上的节点
     *
     * @param text 要查找的文本
     * @param displayId 屏幕ID
     * @return 与文本相同的节点列表，找不到则返回空
     */
    public List<AccessibilityNodeInfo> findNodesByText(String text, int displayId) {
        List<AccessibilityNodeInfo> accessibilityNodeInfos = new ArrayList<>();
        Stack<AccessibilityNodeInfo> nodeStack = new Stack<>();
        AccessibilityNodeInfo node = getRootInActiveWindowWithRetry();
        nodeStack.add(node);
        while (!nodeStack.isEmpty()) {
            node = nodeStack.pop();
            if (node != null && node.getText() != null && node.getText().toString().equals(text)) {
                accessibilityNodeInfos.add(node);
            }
            if (node == null || node.getChildCount() == 0) {
                continue;
            }
            //获得节点的子节点，对于二叉树就是获得节点的左子结点和右子节点
            int childCount = node.getChildCount();
            for (int i = 0; i < childCount; i++) {
                AccessibilityNodeInfo child = node.getChild(i);
                if (child != null) {
                    nodeStack.push(child);
                }
            }
        }
        if (accessibilityNodeInfos.size() > 0) {
            return accessibilityNodeInfos;
        } else {
            return null;
        }
    }

    /**
     * 模拟输入，低版本的输入有所不同，读者请自行百度
     *
     * @param nodeInfo nodeInfo
     * @param text     text
     */
    public void inputText(AccessibilityNodeInfo nodeInfo, String text) {
        Bundle arguments = new Bundle();
        arguments.putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text);
        nodeInfo.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments);
    }

    protected void sleep(long ms) {
        try {
            Thread.sleep(ms);
        } catch (Exception e) {
            FileLogger.e(TAG, "sleep error!", e);
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB001);
        }
    }

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
//        FileLogger.i(TAG, "AccessibilityEvent: " + event.toString());
        if (event.getEventType() == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
            // 获取包名和类名
            String packageName = event.getPackageName() != null ? event.getPackageName().toString() : "";
            String className = event.getClassName() != null ? event.getClassName().toString() : "";
            // 获取窗口类型和Display ID
            int windowType = 0;
            AccessibilityNodeInfo eventSource = event.getSource();
            if (eventSource != null) {
                AccessibilityWindowInfo windowInfo = eventSource.getWindow();
                if (windowInfo != null) {
                    windowType = windowInfo.getType(); // 获取窗口类型
                    FileLogger.i(TAG, "AccessibilityNodeInfo: " + eventSource + ", AccessibilityWindowInfo: " + windowInfo + ", root: " + windowInfo.getRoot());
                    // 不是主屏的话，忽略
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        if(windowInfo.getDisplayId() != Display.DEFAULT_DISPLAY){
                            FileLogger.i(TAG, "ignore non-default display: " + windowInfo.getDisplayId());
                            eventSource.recycle(); // 释放资源
                            return;
                        }
                    } else {
                        FileLogger.w(TAG, "Android API Version < R");
                    }
                } else {
                    FileLogger.w(TAG, "windowInfo is null");
                }
                eventSource.recycle(); // 释放资源
            } else {
                FileLogger.w(TAG, "eventSource is null");
            }
            int displayId = event.getWindowId();
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                displayId = event.getDisplayId(); // 获取Display ID
            }
            // 排除掉FrameLayout等非Activity的类名
            FileLogger.i(TAG, "TYPE_WINDOW_STATE_CHANGED, displayId: " + displayId + ", windowType: " + windowType + ", packageName: " + packageName + ", className: " + className);
            if (GlobalConfig.getInstance().getBlackActivityNames().contains(className)) {
                FileLogger.i(TAG, "ignore FrameLayout or RelativeLayout");
                return;
            }
            currentActivityName = className;
            FileLogger.i(TAG, "currentActivityName: " + currentActivityName);

        } else if (event.getEventType() == AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED) {
            // 监听toast事件
            String toastText = event.getText().toString();
            FileLogger.i(TAG, "Toast: " + toastText);
            currentToastText = toastText;
        }
        // 处理事件（如点击、滑动、文本输入）
//        if (event.getEventType() == AccessibilityEvent.TYPE_VIEW_CLICKED) {
//            AccessibilityNodeInfo node = event.getSource();
//            if (node != null) {
////                performClick(node); // 自定义点击逻辑
//                node.recycle(); // 必须释放资源
//            }
//            FileLogger.i(TAG, "TYPE_VIEW_CLICKED");
//        }
//        FileLogger.i(TAG, getRootInActiveWindow().toString());
    }

    @Override
    public void onInterrupt() {
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public void performScrollRight(AccessibilityNodeInfo info, String nextAction) {
        Rect rect = new Rect();
        info.getBoundsInScreen(rect);
        FileLogger.i(TAG, "rect.right:" + rect.right + ",rect.left :" + rect.left + ",rect.widhth:" + (rect.width() * 0.75));
        Point point = new Point(rect.left + (int) (rect.width() * 0.75), (rect.top + rect.bottom) / 2);
        gesture(point.x, point.y, rect.left, point.y, 0, GESTURE_DURATION, Constant.EVENT_VALUE_DIRECTION_RIGHT, nextAction, info);
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public void performScrollLeft(AccessibilityNodeInfo nodeInfo, String nextAction) {
        Rect rect = new Rect();
        nodeInfo.getBoundsInScreen(rect);
        FileLogger.i(TAG, "rect.right:" + rect.right + ",rect.left :" + rect.left);
        Point point = new Point((rect.left + rect.right) / 2, (rect.top + rect.bottom) / 2);
        gesture(point.x, point.y, point.x + (int) (rect.width() * 0.75), point.y, 0,
                GESTURE_DURATION, Constant.EVENT_VALUE_DIRECTION_LEFT, nextAction, nodeInfo);
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public void performScrollUp(AccessibilityNodeInfo nodeInfo, String nextAction) {
        Rect rect = new Rect();
        nodeInfo.getBoundsInScreen(rect);
        FileLogger.d(TAG, "rect.right:" + rect.right + ",rect.left :" + rect.left);
        Point point = new Point((rect.left + rect.right) / 2, (rect.top + rect.bottom) / 2);
        gesture(point.x, point.y, point.x, point.y + (int) (rect.height() * 0.75),
                0, GESTURE_DURATION, Constant.EVENT_VALUE_DIRECTION_UP, nextAction, nodeInfo);
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public void performScrollDown(AccessibilityNodeInfo nodeInfo, String nextAction) {
        Rect rect = new Rect();
        nodeInfo.getBoundsInScreen(rect);
        FileLogger.d(TAG, "rect.top:" + rect.top + ",rect :" + rect.bottom);
        Point point = new Point((rect.left + rect.right) / 2, (rect.top + rect.bottom) / 2);
        gesture(point.x, point.y, point.x, rect.top, 0, GESTURE_DURATION, "down", nextAction, nodeInfo);
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public boolean isCanScrollVertical(AccessibilityNodeInfo info) {
        return info.isScrollable();
    }

    /**
     * 执行手势操作的通用方法
     * @param displayId 显示屏ID，0表示默认屏幕
     * @param path 手势路径
     * @param duration 手势持续时间
     * @return 是否成功
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    private boolean performGestureWithDisplayId(int displayId, Path path, int duration) {
        // 如果是默认屏幕(0)，使用普通方式
        if (displayId == 0) {
            GestureDescription gesture = new GestureDescription.Builder()
                    .addStroke(new GestureDescription.StrokeDescription(path, 0, duration))
                    .build();
            return dispatchGesture(gesture, null, null);
        }

        // 如果不是默认屏幕，但Android版本不支持多屏，返回失败
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
            FileLogger.e(TAG, "Multi-screen gestures not supported on Android version " + Build.VERSION.SDK_INT +
                      " (requires API " + Build.VERSION_CODES.R + "+) for displayId: " + displayId);
            return false;
        }
        // 多屏方式 - 使用Builder模式设置displayId
        GestureDescription.StrokeDescription stroke = new GestureDescription.StrokeDescription(path, 0, duration);

        GestureDescription.Builder gestureBuilder = new GestureDescription.Builder();
        gestureBuilder.addStroke(stroke);
        gestureBuilder.setDisplayId(displayId);

        GestureDescription gesture = gestureBuilder.build();
        return dispatchGesture(gesture, null, null);
    }

    /**
     * 执行异步手势操作的通用方法
     * @param displayId 显示屏ID，0表示默认屏幕
     * @param path 手势路径
     * @param duration 手势持续时间
     * @param callback 手势执行回调
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    private void performAsyncGestureWithDisplayId(int displayId, Path path, int duration, GestureCallback callback) {
        GestureResultCallback gestureCallback = new GestureResultCallback() {
            @Override
            public void onCompleted(GestureDescription gestureDescription) {
                if (callback != null) {
                    callback.onSuccess();
                }
            }
            @Override
            public void onCancelled(GestureDescription gestureDescription) {
                FileLogger.w(TAG, "Click gesture was cancelled");
                if (callback != null) {
                    callback.onCancelled();
                }
            }
        };

        // 如果是默认屏幕(0)，使用普通方式
        if (displayId == 0) {
            GestureDescription gesture = new GestureDescription.Builder()
                    .addStroke(new GestureDescription.StrokeDescription(path, 0, duration))
                    .build();
            boolean dispatched = dispatchGesture(gesture, gestureCallback, null);
            if (!dispatched) {
                FileLogger.e(TAG, "Failed to dispatch gesture");
                FailureContextHolder.setFailureIfNotSet(FailureCode.EI011);
                if (callback != null) {
                    callback.onFailure("Failed to dispatch gesture");
                }
            }
            return;
        }

        // 如果不是默认屏幕，但Android版本不支持多屏，返回失败
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
            FileLogger.e(TAG, "Multi-screen gestures not supported on Android version " + Build.VERSION.SDK_INT +
                      " (requires API " + Build.VERSION_CODES.R + "+) for displayId: " + displayId);
            FailureContextHolder.setFailureIfNotSet(FailureCode.EI011);
            if (callback != null) {
                callback.onFailure("Multi-screen gestures not supported on Android version " + Build.VERSION.SDK_INT +
                                  " (requires API " + Build.VERSION_CODES.R + "+) for displayId: " + displayId);
            }
            return;
        }

        // 多屏方式 - 使用Builder模式设置displayId
        GestureDescription.StrokeDescription stroke = new GestureDescription.StrokeDescription(path, 0, duration);

        GestureDescription.Builder gestureBuilder = new GestureDescription.Builder();
        gestureBuilder.addStroke(stroke);
        gestureBuilder.setDisplayId(displayId);

        GestureDescription gesture = gestureBuilder.build();

        boolean dispatched = dispatchGesture(gesture, gestureCallback, null);
        if (!dispatched) {
            FileLogger.e(TAG, "Failed to dispatch gesture on displayId " + displayId);
            FailureContextHolder.setFailureIfNotSet(FailureCode.EI011);
            if (callback != null) {
                callback.onFailure("Failed to dispatch gesture on displayId " + displayId);
            }
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public boolean performClick(int x, int y, int displayId) {
        FileLogger.i(TAG, "performClick: x:" + x + ",y:" + y + ",displayId:" + displayId);
        Path path = new Path();
        path.moveTo(x, y);
        path.lineTo(x, y);

        return performGestureWithDisplayId(displayId, path, 100);
    }

    /**
     * 异步执行点击操作
     * @param x X坐标
     * @param y Y坐标
     * @param displayId 显示屏ID，0表示默认屏幕
     * @param callback 手势执行回调
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public void performClickAsync(int x, int y, int displayId, GestureCallback callback) {
        FileLogger.i(TAG, "performClickAsync: x:" + x + ",y:" + y + ",displayId:" + displayId);
        Path path = new Path();
        path.moveTo(x, y);
        path.lineTo(x, y);

        performAsyncGestureWithDisplayId(displayId, path, 100, callback);
    }

    /**
     * 异步长按操作
     * @param x X坐标
     * @param y Y坐标
     * @param duration 长按持续时间
     * @param displayId 显示屏ID，0表示默认屏幕
     * @param callback 回调
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public void performLongClickAsync(int x, int y, int duration, int displayId, GestureCallback callback) {
        FileLogger.i(TAG, "performLongClickAsync: x:" + x + ",y:" + y + ",duration:" + duration + ",displayId:" + displayId);
        Path path = new Path();
        path.moveTo(x, y);
        path.lineTo(x, y);

        performAsyncGestureWithDisplayId(displayId, path, duration, callback);
    }

    /**
     * 执行滑动操作
     * @param startX 起始X坐标
     * @param startY 起始Y坐标
     * @param endX 结束X坐标
     * @param endY 结束Y坐标
     * @param duration 持续时间
     * @param displayId 显示屏ID，0表示默认屏幕
     * @return 是否成功
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public boolean performSwipe(int startX, int startY, int endX, int endY, int duration, int displayId) {
        FileLogger.i(TAG, "performSwipe: startX:" + startX + ",startY:" + startY
                + ",endX:" + endX + ",endY:" + endY + ",displayId:" + displayId);
        Path path = new Path();
        path.moveTo(startX, startY);
        path.lineTo(endX, endY);

        return performGestureWithDisplayId(displayId, path, duration);
    }

    @RequiresApi(api = Build.VERSION_CODES.R)
    public void performMultiTouch(int[][] points, int duration) {
        GestureDescription.Builder builder = new GestureDescription.Builder();

        for (int[] point : points) {
            Path path = new Path();
            path.moveTo(point[0], point[1]);
            builder.addStroke(new GestureDescription.StrokeDescription(path, 0, duration));
        }

        dispatchGesture(builder.build(), null, null);
    }

    @RequiresApi(api = Build.VERSION_CODES.R)
    public void screenshot(int width, int height,String timeStampString) {
        FileLogger.i(TAG, "screenshot: trying to capture");
        takeScreenshot(0, getMainExecutor(), new AccessibilityService.TakeScreenshotCallback() { // from class: com.stardust.autojs.core.accessibility.AccessibilityService.2
            @Override // android.accessibilityservice.AccessibilityService.TakeScreenshotCallback
            public void onFailure(int i) {
                FileLogger.i(TAG, "screenshot: failed! " + i);
            }

            @Override // android.accessibilityservice.AccessibilityService.TakeScreenshotCallback
            public void onSuccess(@NonNull AccessibilityService.ScreenshotResult screenshotResult) {
                FileLogger.i(TAG, "screenshot: success");
                try {
                    Bitmap bitmap = Bitmap.createBitmap(Bitmap.wrapHardwareBuffer(screenshotResult.getHardwareBuffer(),
                                    screenshotResult.getColorSpace()),
                            0, 0, width, height); //这里得到图片
                    ScreenCapture.saveBitmap(bitmap,timeStampString);
                } catch (Exception e) {
                    FileLogger.e(TAG, "screenshot: error!"+ e);
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 获取指定显示屏的所有窗口根节点，用于UI Dump等操作
     * @param displayId 显示屏ID，0表示主屏幕
     */
    public List<AccessibilityNodeInfo> getAllWindowRoots(int displayId) {
        Set<AccessibilityNodeInfo> roots = new HashSet<>();

        List<AccessibilityWindowInfo> windows = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
             windows = getWindowsOnAllDisplays().get(displayId);
             if (windows == null) {
                 FileLogger.w(TAG, "No windows found for displayId: " + displayId);
                 FailureContextHolder.setFailureIfNotSet(FailureCode.SI001);
                 return new ArrayList<>(roots);
             }
             for (int i = 0; i < windows.size(); i++) {
                 AccessibilityWindowInfo windowInfo = windows.get(i);
                 if (windowInfo == null) continue;
                 AccessibilityNodeInfo root = windowInfo.getRoot();
                 if (root != null) {
                     roots.add(root);
                 }
             }
             return new ArrayList<>(roots);

        }

        // android 11及以下版本
        // 如果displayId大于0，不支持
        // 如果displayId为0，获取当前活动窗口的根节点和所有窗口的根节点
        if (displayId == 0) {
            AccessibilityNodeInfo activeRoot = getRootInActiveWindow();
            if (activeRoot != null) {
                roots.add(activeRoot);
            } else {
                FileLogger.w(TAG, "Active window root not found in getAllWindowRoots.");
                FailureContextHolder.setFailureIfNotSet(FailureCode.EI008);
            }
            windows = getWindows();
            if (windows != null) {
                for (AccessibilityWindowInfo windowInfo : windows) {
                    if (windowInfo == null) continue;
                    AccessibilityNodeInfo root = windowInfo.getRoot();
                    if (root != null) {
                        roots.add(root);
                    }
                }
            }
            FileLogger.i(TAG, "Found " + roots.size() + " window roots for displayId: " + displayId);
        }
        return new ArrayList<>(roots);
    }

    /**
     * 执行UI Dump - 支持多屏操作
     * @param timeStampString 时间戳字符串
     * @param widthPixels 屏幕宽度
     * @param heightPixels 屏幕高度
     * @param displayId 显示屏ID
     * @return UI dump文件名
     * @throws ActionException 操作异常
     */
    public String performUiDump(String timeStampString, int widthPixels, int heightPixels, int displayId, String screenName) throws ActionException {
        List<AccessibilityNodeInfo> allRoots = null;
        File dumpFile = null;
        FileOutputStream fos = null;
        String outputFileName = null;
        long startTime = System.currentTimeMillis();

        try {
            if (widthPixels <= 0 || heightPixels <= 0) {
                FileLogger.e(TAG, "Invalid display dimensions provided for UI Dump: " + widthPixels + "x" + heightPixels);
                FailureContextHolder.setFailureIfNotSet(FailureCode.AB001);
                return null;
            }

            File outputDir = new File(Constant.UIDUMP_DATA_PATH);
            if (!outputDir.exists() && !outputDir.mkdirs()) {
                FileLogger.e(TAG, "Failed to create uidumps directory: " + outputDir.getAbsolutePath());
                FailureContextHolder.setFailureIfNotSet(FailureCode.EI006);
                return null;
            }

            String screenSuffix = "";
            if (displayId > 0) {
                // 非默认屏幕，使用_screenName_格式
                screenSuffix = "_" + screenName;
            }
            outputFileName = "uidump" + screenSuffix + "_" + timeStampString + ".xml";
            dumpFile = new File(outputDir, outputFileName);
            fos = new FileOutputStream(dumpFile);

            XmlSerializer serializer = Xml.newSerializer();
            serializer.setFeature("http://xmlpull.org/v1/doc/features.html#indent-output", true);
            serializer.setOutput(fos, "UTF-8");
            serializer.startDocument("UTF-8", true);
            serializer.startTag("", "hierarchy");

            UIHierarchyDumper dumper = new UIHierarchyDumper(widthPixels, heightPixels);
            // 获取指定displayId的窗口根节点
            allRoots = getAllWindowRoots(displayId);

            int treeIndex = 0;
            for (AccessibilityNodeInfo rootNode : allRoots) {
                if (rootNode != null) {
                    dumper.dumpTreeNodesToSerializer(rootNode, serializer, MAX_TRAVERSAL_DEPTH, treeIndex++);
                }
            }

            serializer.endTag("", "hierarchy");
            serializer.endDocument();
            fos.flush();

            long endTime = System.currentTimeMillis();
            FileLogger.i(TAG, "UI dump for displayId " + displayId + " successfully written to: " + dumpFile.getName() + " in " + (endTime - startTime) + " ms.");
            EventBus.getDefault().post(new FileUploadEvent(dumpFile.getAbsolutePath()));
            return dumpFile.getName();

        } catch (IOException e) {
            String msg = "IOException during UI dump to " + dumpFile.getAbsolutePath() + ": " + e.getMessage();
            FileLogger.e(TAG, msg);
            if (dumpFile.exists()) { dumpFile.delete(); }
            throw new ActionException(msg, FailureCode.EI006,e);
        } catch (Exception e) {
            String msg = "Unexpected exception during UI dump: " + e.getMessage();
            FileLogger.e(TAG, "Unexpected exception during UI dump: " + e.getMessage());
            if (dumpFile != null && dumpFile.exists()) { dumpFile.delete(); }
            throw new ActionException(msg, FailureCode.EI006, e);
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (allRoots != null) {
                for (AccessibilityNodeInfo root : allRoots) {
                    if (root != null) {
                        try {
                            root.recycle();
                        } catch (IllegalStateException ise) {
                            ise.printStackTrace();
                        }
                    }
                }
            }
        }
    }
}
