<?xml version="1.0" encoding="utf-8"?>
<com.xiaopeng.xui.widget.XConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.xiaopeng.xui.widget.XConstraintLayout
        android:id="@+id/cl_campaign_event_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/x_bg_toast"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.xiaopeng.xui.widget.XTextView
            android:id="@+id/tv_ota_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginStart="30dp"
            android:textColor="@color/x_theme_text_01"
            android:textSize="@dimen/x_font_title_02_size"
            app:layout_constraintTop_toTopOf="@id/cl_campaign_event_list"
            app:layout_constraintStart_toStartOf="@id/cl_campaign_event_list" />

        <com.xiaopeng.xui.widget.XTextView
            android:id="@+id/tv_statistics"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:textColor="@color/x_theme_text_01"
            android:textSize="@dimen/x_font_title_02_size"
            android:text="0/0/0"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_ota_version"
            app:layout_constraintStart_toEndOf="@id/tv_ota_version" />

        <com.xiaopeng.xui.widget.XButton
            android:id="@+id/btn_retry_failed"
            style="@style/XButton.V5.Real.Small.Primary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:textSize="@dimen/x_font_body_03_size"
            android:text="@string/factory_mode_retry_failed_cases"
            android:visibility="gone"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_ota_version"
            app:layout_constraintEnd_toStartOf="@id/btn_execute_all" />

        <com.xiaopeng.xui.widget.XButton
            android:id="@+id/btn_execute_all"
            style="@style/XButton.V5.Real.Small.Primary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="30dp"
            android:textSize="@dimen/x_font_body_03_size"
            android:text="@string/factory_mode_execute_all_components"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_ota_version"
            app:layout_constraintEnd_toEndOf="parent" />

        <com.xiaopeng.xui.widget.XButton
            android:id="@+id/btn_ota_event"
            style="@style/XButton.V5.Real.Small.Primary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="30dp"
            android:textSize="@dimen/x_font_body_03_size"
            android:visibility="gone"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_ota_version"
            app:layout_constraintEnd_toStartOf="@id/btn_execute_all" />

        <com.xiaopeng.xui.widget.XButton
            android:id="@+id/btn_operate"
            style="@style/XButton.V5.Real.Small.Primary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="30dp"
            android:textSize="@dimen/x_font_body_03_size"
            android:visibility="gone"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_ota_version"
            app:layout_constraintEnd_toStartOf="@id/btn_execute_all" />

        <com.xiaopeng.xui.widget.XTextView
            android:id="@+id/tv_operate_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:textColor="@color/x_theme_primary_negative_normal"
            android:textSize="@dimen/x_font_title_03_size"
            android:fontFamily="@string/x_font_typeface_bold"
            android:visibility="gone"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_ota_version"
            app:layout_constraintEnd_toStartOf="@id/btn_operate" />

        <!-- table title -->
        <com.xiaopeng.xui.widget.XLinearLayout
            android:id="@+id/ll_campaign_event_list_header"
            android:layout_width="match_parent"
            android:layout_height="@dimen/directory_list_height"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="30dp"
            android:layout_marginTop="10dp"
            android:background="@color/x_theme_text_05"
            app:layout_constraintStart_toStartOf="@id/cl_campaign_event_list"
            app:layout_constraintTop_toBottomOf="@id/tv_ota_version">

            <com.xiaopeng.xui.view.XView style="@style/TableHeaderLine" />

            <com.xiaopeng.xui.widget.XTextView
                android:id="@+id/tv_header_status"
                android:layout_width="200dp"
                android:layout_height="match_parent"
                android:text="@string/testcase_table_title_status"
                android:textColor="@color/x_theme_text_01"
                android:textSize="@dimen/x_font_body_02_size"
                android:textAlignment="center"
                android:gravity="center" />

            <com.xiaopeng.xui.view.XView style="@style/TableHeaderLine" />

            <com.xiaopeng.xui.widget.XTextView
                android:id="@+id/tv_header_name"
                android:layout_width="600dp"
                android:layout_height="match_parent"
                android:text="@string/testcase_table_title_name"
                android:textColor="@color/x_theme_text_01"
                android:textSize="@dimen/x_font_body_02_size"
                android:textAlignment="center"
                android:gravity="center" />

            <com.xiaopeng.xui.view.XView style="@style/TableHeaderLine" />

            <com.xiaopeng.xui.widget.XTextView
                android:id="@+id/tv_header_operation"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/testcase_table_title_operation"
                android:textColor="@color/x_theme_text_01"
                android:textSize="@dimen/x_font_body_02_size"
                android:textAlignment="center"
                android:gravity="center" />

        </com.xiaopeng.xui.widget.XLinearLayout>

        <!-- list view -->
        <com.xiaopeng.xui.widget.XLinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="10dp"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="30dp"
            app:layout_constraintTop_toBottomOf="@id/ll_campaign_event_list_header"
            app:layout_constraintBottom_toBottomOf="@id/cl_campaign_event_list"
            app:layout_constraintStart_toStartOf="@id/cl_campaign_event_list">

<!--            <ListView-->
<!--                android:id="@+id/list_view"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:cacheColorHint="@android:color/transparent" />-->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_test_case_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:cacheColorHint="@android:color/transparent"/>

        </com.xiaopeng.xui.widget.XLinearLayout>

    </com.xiaopeng.xui.widget.XConstraintLayout>

</com.xiaopeng.xui.widget.XConstraintLayout>