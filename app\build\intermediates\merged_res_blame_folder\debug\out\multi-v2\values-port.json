{"logs": [{"outputFile": "com.xiaopeng.xpautotest.app-mergeDebugResources-45:/values-port/values-port.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9fb248392f055438974c3c6bf62bfc1\\transformed\\appcompat-1.7.0\\res\\values-port\\values-port.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "55", "endOffsets": "106"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fc66c71e6d2f7749ba39454a10f3998a\\transformed\\jetified-lib_xpui-5.6.3\\res\\values-port\\values-port.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,110,118,125,128,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,159,212,260,316,372,428,485,542,598,653,710,766,818,867,929,982,1037,1089,1148,1205,1256,1307,1373,1442,1512,1566,1621,1675,1727,1779,1834,1881,1937,1992,2043,2094,2145,2196,2250,2304,2358,2411,2464,2517,2570,2623,2675,2727,2779,2831,2885,2956,3017,3072,3147,3206,3255,3307,3359,3412,3462,3516,3578,3631,3679,3726,3778,3829,3895,3951,4013,4065,4139,4198,4278,4331,4404,4458,4511,4561,4617,4671,4722,4782,4844,4895,4958,5008,5055,5106,5159,5211,5261,5308,5360,5407,5464,5522,5578,6092,6588,6953,7064,7440", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,109,117,124,127,134,146", "endColumns": "51,51,52,47,55,55,55,56,56,55,54,56,55,51,48,61,52,54,51,58,56,50,50,65,68,69,53,54,53,51,51,54,46,55,54,50,50,50,50,53,53,53,52,52,52,52,52,51,51,51,51,53,70,60,54,74,58,48,51,51,52,49,53,61,52,47,46,51,50,65,55,61,51,73,58,79,52,72,53,52,49,55,53,50,59,61,50,62,49,46,50,52,51,49,46,51,46,56,57,55,12,12,12,12,12,12", "endOffsets": "102,154,207,255,311,367,423,480,537,593,648,705,761,813,862,924,977,1032,1084,1143,1200,1251,1302,1368,1437,1507,1561,1616,1670,1722,1774,1829,1876,1932,1987,2038,2089,2140,2191,2245,2299,2353,2406,2459,2512,2565,2618,2670,2722,2774,2826,2880,2951,3012,3067,3142,3201,3250,3302,3354,3407,3457,3511,3573,3626,3674,3721,3773,3824,3890,3946,4008,4060,4134,4193,4273,4326,4399,4453,4506,4556,4612,4666,4717,4777,4839,4890,4953,5003,5050,5101,5154,5206,5256,5303,5355,5402,5459,5517,5573,6087,6583,6948,7059,7435,8075"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,111,119,126,129,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "111,163,215,268,316,372,428,484,541,598,654,709,766,822,874,923,985,1038,1093,1145,1204,1261,1312,1363,1429,1498,1568,1622,1677,1731,1783,1835,1890,1937,1993,2048,2099,2150,2201,2252,2306,2360,2414,2467,2520,2573,2626,2679,2731,2783,2835,2887,2941,3012,3073,3128,3203,3262,3311,3363,3415,3468,3518,3572,3634,3687,3735,3782,3834,3885,3951,4007,4069,4121,4195,4254,4334,4387,4460,4514,4567,4617,4673,4727,4778,4838,4900,4951,5014,5064,5111,5162,5215,5267,5317,5364,5416,5463,5520,5578,5634,6148,6644,7009,7120,7496", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,110,118,125,128,135,147", "endColumns": "51,51,52,47,55,55,55,56,56,55,54,56,55,51,48,61,52,54,51,58,56,50,50,65,68,69,53,54,53,51,51,54,46,55,54,50,50,50,50,53,53,53,52,52,52,52,52,51,51,51,51,53,70,60,54,74,58,48,51,51,52,49,53,61,52,47,46,51,50,65,55,61,51,73,58,79,52,72,53,52,49,55,53,50,59,61,50,62,49,46,50,52,51,49,46,51,46,56,57,55,12,12,12,12,12,12", "endOffsets": "158,210,263,311,367,423,479,536,593,649,704,761,817,869,918,980,1033,1088,1140,1199,1256,1307,1358,1424,1493,1563,1617,1672,1726,1778,1830,1885,1932,1988,2043,2094,2145,2196,2247,2301,2355,2409,2462,2515,2568,2621,2674,2726,2778,2830,2882,2936,3007,3068,3123,3198,3257,3306,3358,3410,3463,3513,3567,3629,3682,3730,3777,3829,3880,3946,4002,4064,4116,4190,4249,4329,4382,4455,4509,4562,4612,4668,4722,4773,4833,4895,4946,5009,5059,5106,5157,5210,5262,5312,5359,5411,5458,5515,5573,5629,6143,6639,7004,7115,7491,8131"}}]}]}