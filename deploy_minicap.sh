#!/bin/bash

# minicap部署脚本
# 用于手动部署minicap文件到Android设备

echo "=== Minicap部署脚本 ==="

# 检查adb连接
if ! command -v adb &> /dev/null; then
    echo "错误: 未找到adb命令，请确保Android SDK已安装并添加到PATH"
    exit 1
fi

# 检查设备连接
DEVICE_COUNT=$(adb devices | grep -v "List of devices" | grep -c "device")
if [ $DEVICE_COUNT -eq 0 ]; then
    echo "错误: 未找到连接的Android设备"
    echo "请确保设备已连接并启用USB调试"
    exit 1
fi

echo "找到 $DEVICE_COUNT 个设备"

# 获取设备架构
ABI=$(adb shell getprop ro.product.cpu.abi)
echo "设备架构: $ABI"

# 设置minicap文件路径
MINICAP_DIR="app/src/main/assets/minicap/$ABI"
MINICAP_BIN="$MINICAP_DIR/minicap"
MINICAP_SO="$MINICAP_DIR/minicap.so"

# 检查minicap文件是否存在
if [ ! -f "$MINICAP_BIN" ]; then
    echo "错误: 未找到minicap文件: $MINICAP_BIN"
    echo "请先从STF项目下载对应架构的minicap文件"
    exit 1
fi

if [ ! -f "$MINICAP_SO" ]; then
    echo "错误: 未找到minicap.so文件: $MINICAP_SO"
    echo "请先从STF项目下载对应架构的minicap.so文件"
    exit 1
fi

echo "找到minicap文件:"
echo "  - $MINICAP_BIN"
echo "  - $MINICAP_SO"

# 创建目标目录
echo "创建目标目录..."
adb shell "mkdir -p /sdcard/AutoTest/minicap"

# 推送文件
echo "推送minicap文件..."
adb push "$MINICAP_BIN" /sdcard/AutoTest/minicap/minicap
if [ $? -ne 0 ]; then
    echo "错误: 推送minicap失败"
    exit 1
fi

adb push "$MINICAP_SO" /sdcard/AutoTest/minicap/minicap.so
if [ $? -ne 0 ]; then
    echo "错误: 推送minicap.so失败"
    exit 1
fi

# 设置权限
echo "设置文件权限..."
adb shell "chmod 755 /sdcard/AutoTest/minicap/minicap"
adb shell "chmod 644 /sdcard/AutoTest/minicap/minicap.so"

# 验证部署
echo "验证部署结果..."
adb shell "ls -la /sdcard/AutoTest/minicap/*"

# 测试minicap
echo "测试minicap..."
SCREEN_SIZE=$(adb shell wm size | grep "Physical size" | cut -d: -f2 | tr -d ' ')
if [ -z "$SCREEN_SIZE" ]; then
    SCREEN_SIZE="1920x1080"
fi

echo "屏幕尺寸: $SCREEN_SIZE"

# 启动minicap测试（5秒后自动停止）
echo "启动minicap测试（5秒）..."
adb shell "cd /sdcard/AutoTest/minicap && timeout 5 LD_LIBRARY_PATH=. ./minicap -P ${SCREEN_SIZE}@${SCREEN_SIZE}/0 -S" &
MINICAP_PID=$!

sleep 2

# 检查minicap是否运行
if adb shell "ps | grep minicap" > /dev/null; then
    echo "✅ minicap启动成功！"
else
    echo "❌ minicap启动失败"
    echo "请检查设备权限和文件完整性"
fi

# 停止测试
kill $MINICAP_PID 2>/dev/null
adb shell "pkill minicap" 2>/dev/null

echo ""
echo "=== 部署完成 ==="
echo "现在可以在应用中使用屏幕录制功能了"
echo ""
echo "如果遇到问题，请检查:"
echo "1. 设备是否有root权限或系统级应用权限"
echo "2. minicap文件是否与设备架构匹配"
echo "3. /data/local/tmp目录是否可写"
echo ""
echo "手动测试命令:"
echo "adb shell"
echo "cd /sdcard/AutoTest/minicap"
echo "LD_LIBRARY_PATH=. ./minicap -P ${SCREEN_SIZE}@${SCREEN_SIZE}/0 -S"
