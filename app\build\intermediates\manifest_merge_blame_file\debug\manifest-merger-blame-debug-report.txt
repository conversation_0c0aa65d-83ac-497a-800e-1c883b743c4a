1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.xiaopeng.xpautotest"
4    android:sharedUserId="android.uid.system"
5    android:sharedUserMaxSdkVersion="32"
6    android:versionCode="9999"
7    android:versionName="1.0.0-SNAPSHOT" >
8
9    <uses-sdk
10        android:minSdkVersion="28"
11        android:targetSdkVersion="30" />
12
13    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
13-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:7:5-75
13-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
14-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:8:5-74
14-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:8:22-71
15    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
15-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:10:5-76
15-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:10:22-73
16    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
16-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:11:5-76
16-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:11:22-73
17    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
17-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:12:5-79
17-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:12:22-76
18    <uses-permission android:name="android.permission.INTERNET" />
18-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:13:5-67
18-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:13:22-64
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:14:5-79
19-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:14:22-76
20    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
20-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:15:5-75
20-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:15:22-72
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:16:5-81
21-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:16:22-78
22    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
22-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:17:5-81
22-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:17:22-78
23    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
23-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:18:5-82
23-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:18:22-79
24    <uses-permission android:name="android.permission.REBOOT" />
24-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:19:5-65
24-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:19:22-62
25    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
25-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:20:5-78
25-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:20:22-75
26    <uses-permission android:name="android.permission.CONNECTIVITY_INTERNAL" />
26-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:21:5-80
26-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:21:22-77
27    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
27-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:22:5-84
27-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:22:22-81
28    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
28-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:23:5-88
28-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:23:22-85
29    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
29-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:24:5-82
29-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:24:22-79
30    <uses-permission android:name="android.permission.WAKE_LOCK" />
30-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:25:5-68
30-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:25:22-65
31    <uses-permission android:name="android.permission.DEVICE_POWER" />
31-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:26:5-71
31-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:26:22-68
32    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
32-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:27:5-80
32-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:27:22-77
33    <uses-permission android:name="android.permission.READ_LOGS" />
33-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:28:5-68
33-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:28:22-65
34    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
34-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:29:5-78
34-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:29:22-75
35    <uses-permission android:name="android.permission.RECOVERY" />
35-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:30:5-67
35-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:30:22-64
36    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
36-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:31:5-80
36-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:31:22-77
37    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
37-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:32:5-84
37-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:32:22-81
38    <uses-permission android:name="android.permission.SUPER_APPLICATION_RUNNING" />
38-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:33:5-84
38-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:33:22-81
39    <uses-permission android:name="android.car.permission.CAR_VENDOR_EXTENSION" />
39-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:34:5-83
39-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:34:22-80
40    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
40-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:35:5-85
40-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:35:22-82
41    <uses-permission android:name="android.permission.MEDIA_PROJECTION" />
41-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:36:5-75
41-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:36:22-72
42    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
42-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:37:5-77
42-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:37:22-74
43    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
43-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:38:5-94
43-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:38:22-91
44    <uses-permission android:name="com.xiaopeng.permission.OTA_SERVICE" />
44-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:39:5-75
44-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:39:22-72
45    <uses-permission android:name="com.xiaopeng.permission.CAR_SERVICE" />
45-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:40:5-75
45-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:40:22-72
46    <uses-permission android:name="com.xiaopeng.permission.ACTIVITY" />
46-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:41:5-72
46-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:41:22-69
47    <uses-permission android:name="com.xiaopeng.permission.SERVICE" />
47-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:42:5-71
47-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:42:22-68
48    <uses-permission android:name="com.xiaopeng.permission.BROADCAST" />
48-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:43:5-73
48-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:43:22-70
49    <uses-permission android:name="xiaopeng.permission.DATA_SERVICE" />
49-->[com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:9:5-72
49-->[com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:9:22-69
50    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
50-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:24:5-81
50-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:24:22-78
51    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
51-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:25:5-79
51-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:25:22-76
52    <uses-permission android:name="com.xiaopeng.permission.DATA_SERVICE" />
52-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:26:5-76
52-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:26:22-73
53    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
53-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:27:5-85
53-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:27:22-82
54    <uses-permission android:name="android.permission.GET_TASKS" />
54-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:29:5-68
54-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:29:22-65
55    <uses-permission android:name="android.permission.CALL_PHONE" />
55-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:19:5-69
55-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:19:22-66
56    <uses-permission android:name="android.permission.READ_SMS" />
56-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:20:5-67
56-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:20:22-64
57    <uses-permission android:name="com.xiaopeng.permission.SYSTEM_DELEGATE" />
57-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:21:5-79
57-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:21:22-76
58    <uses-permission android:name="android.permission.REORDER_TASKS" />
58-->[com.xiaopeng.lib:lib_utils:1.7.5.9] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-1.7.5.9\AndroidManifest.xml:8:5-72
58-->[com.xiaopeng.lib:lib_utils:1.7.5.9] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-1.7.5.9\AndroidManifest.xml:8:22-69
59    <uses-permission android:name="android.permission.XIAOPENG_APP" />
59-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:11:5-71
59-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:11:22-68
60
61    <permission
61-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
62        android:name="com.xiaopeng.xpautotest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
62-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
63        android:protectionLevel="signature" />
63-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
64
65    <uses-permission android:name="com.xiaopeng.xpautotest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
65-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
65-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
66
67    <application
67-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:44:5-130:19
68        android:name="com.xiaopeng.xpautotest.App"
68-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:45:9-51
69        android:allowBackup="false"
69-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:32:9-36
70        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
70-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
71        android:configChanges="keyboard|keyboardHidden|navigation"
71-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:46:9-67
72        android:debuggable="true"
73        android:extractNativeLibs="true"
73-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:47:9-41
74        android:icon="@mipmap/ic_launcher"
74-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:49:9-43
75        android:label="@string/app_name"
75-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:50:9-41
76        android:networkSecurityConfig="@xml/network_security_config"
76-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:48:9-69
77        android:supportsRtl="true"
77-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:51:9-35
78        android:testOnly="true"
79        android:theme="@style/autotest_AppTheme" >
79-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:52:9-49
80        <meta-data
80-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:54:9-56:109
81            android:name="com.xiaopeng.lib.lib_feature_modules"
81-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:55:13-64
82            android:value="com.xiaopeng.carcontrol,com.xiaopeng.caraccount,com.xiaopeng.carspeechservice" />
82-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:56:13-106
83
84        <activity
84-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:57:9-68:20
85            android:name="com.xiaopeng.xpautotest.ui.MainActivity"
85-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:58:13-67
86            android:configChanges="navigation|colorMode|mnc|mcc|fontScale|keyboard|keyboardHidden|screenSize|smallestScreenSize|density|locale|uiMode|orientation|layoutDirection|screenLayout|touchscreen"
86-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:59:13-204
87            android:exported="true"
87-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:60:13-36
88            android:launchMode="singleInstance"
88-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:61:13-48
89            android:theme="@style/autotest_AppTheme" >
89-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:62:13-53
90            <intent-filter>
90-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:64:13-67:29
91                <action android:name="android.intent.action.MAIN" />
91-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:65:17-69
91-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:65:25-66
92
93                <category android:name="android.intent.category.LAUNCHER" />
93-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:66:17-77
93-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:66:27-74
94            </intent-filter>
95        </activity>
96
97        <!-- 性能报告显示Activity -->
98        <activity
98-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:71:9-75:56
99            android:name="com.xiaopeng.xpautotest.ui.PerformanceReportActivity"
99-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:72:13-80
100            android:configChanges="navigation|colorMode|mnc|mcc|fontScale|keyboard|keyboardHidden|screenSize|smallestScreenSize|density|locale|uiMode|orientation|layoutDirection|screenLayout|touchscreen"
100-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:73:13-204
101            android:exported="false"
101-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:74:13-37
102            android:theme="@style/autotest_AppTheme" />
102-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:75:13-53
103
104        <!-- 录屏测试Activity -->
105        <activity
105-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:78:9-82:56
106            android:name="com.xiaopeng.xpautotest.debug.ScreenRecordTestActivity"
106-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:79:13-82
107            android:configChanges="navigation|colorMode|mnc|mcc|fontScale|keyboard|keyboardHidden|screenSize|smallestScreenSize|density|locale|uiMode|orientation|layoutDirection|screenLayout|touchscreen"
107-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:80:13-204
108            android:exported="false"
108-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:81:13-37
109            android:theme="@style/autotest_AppTheme" />
109-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:82:13-53
110
111        <service
111-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:84:9-88:62
112            android:name="com.xiaopeng.xpautotest.service.TestExecutionService"
112-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:85:13-80
113            android:enabled="true"
113-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:86:13-35
114            android:exported="false"
114-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:87:13-37
115            android:foregroundServiceType="connectedDevice" />
115-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:88:13-60
116        <service
116-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:90:9-94:62
117            android:name="com.xiaopeng.xpautotest.service.DebuggingModeService"
117-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:91:13-80
118            android:enabled="true"
118-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:92:13-35
119            android:exported="false"
119-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:93:13-37
120            android:foregroundServiceType="connectedDevice" />
120-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:94:13-60
121        <service
121-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:96:9-107:19
122            android:name="com.xiaopeng.xpautotest.accessibility.AutoTestAccessibilityService"
122-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:97:13-71
123            android:enabled="true"
123-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:99:13-35
124            android:exported="true"
124-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:100:13-36
125            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
125-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:98:13-79
126            <intent-filter>
126-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:101:13-103:29
127                <action android:name="android.accessibilityservice.AccessibilityService" />
127-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:102:17-92
127-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:102:25-89
128            </intent-filter>
129
130            <meta-data
130-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:104:13-106:58
131                android:name="android.accessibilityservice"
131-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:105:17-60
132                android:resource="@xml/service_config" />
132-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:106:17-55
133        </service>
134
135        <receiver
135-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:109:9-115:20
136            android:name="com.xiaopeng.xpautotest.receiver.StartBroadcastReceiver"
136-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:109:19-89
137            android:enabled="true"
137-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:110:13-35
138            android:exported="true" >
138-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:111:13-36
139            <intent-filter>
139-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:112:13-114:29
140                <action android:name="com.xiaoppeng.xpautotest.OPEN_APP_ACTION" />
140-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:113:17-83
140-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:113:25-80
141            </intent-filter>
142        </receiver>
143
144        <service
144-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:116:9-122:19
145            android:name="com.xiaopeng.xpautotest.service.OSSUploadService"
145-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:117:13-76
146            android:exported="true" >
146-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:118:13-36
147            <intent-filter>
147-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:119:13-121:29
148                <action android:name="com.xiaopeng.xpautotest.action.START_OSS_UPLOAD_SERVICE" />
148-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:120:17-98
148-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:120:25-95
149            </intent-filter>
150        </service>
151
152        <!-- 屏幕录制服务 -->
153        <service
153-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:125:9-129:63
154            android:name="com.xiaopeng.xpautotest.service.ScreenRecordService"
154-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:126:13-79
155            android:enabled="true"
155-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:127:13-35
156            android:exported="false"
156-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:128:13-37
157            android:foregroundServiceType="mediaProjection" />
157-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:129:13-60
158        <service
158-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-13:19
159            android:name="com.xiaopeng.xpautotest.trace.service.CanDataCollectService"
159-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-87
160            android:enabled="true"
160-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-35
161            android:exported="false"
161-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
162            android:foregroundServiceType="connectedDevice" >
162-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-60
163        </service>
164        <service android:name="org.eclipse.paho.android.service.MqttService" />
164-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:34:9-80
164-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:34:18-77
165
166        <provider
166-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:36:9-40:20
167            android:name="com.xiaopeng.lib.framework.netchannelmodule.common.ContextNetStatusProvider"
167-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:37:13-103
168            android:authorities="com.xiaopeng.xpautotest.netmodule.provider"
168-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:38:13-70
169            android:exported="false" >
169-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:39:13-37
170        </provider>
171        <provider
171-->[com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:11:9-15:34
172            android:name="com.xiaopeng.lib.apirouter.server.ApiPublisherProvider"
172-->[com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:12:13-82
173            android:authorities="com.xiaopeng.xpautotest.api.publisher"
173-->[com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:13:13-65
174            android:exported="true" />
174-->[com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:14:13-36
175        <provider
175-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:14:9-19:34
176            android:name="com.xiaopeng.lib.feature.XpFeatureProvider"
176-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:15:13-70
177            android:authorities="com.xiaopeng.xpautotest.XpFeatureProvider"
177-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:16:13-69
178            android:exported="false"
178-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:17:13-37
179            android:initOrder="1100" />
179-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:18:13-37
180        <provider
180-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
181            android:name="androidx.startup.InitializationProvider"
181-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
182            android:authorities="com.xiaopeng.xpautotest.androidx-startup"
182-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
183            android:exported="false" >
183-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
184            <meta-data
184-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
185                android:name="androidx.emoji2.text.EmojiCompatInitializer"
185-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
186                android:value="androidx.startup" />
186-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
187            <meta-data
187-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
188                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
188-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
189                android:value="androidx.startup" />
189-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
190            <meta-data
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
191                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
192                android:value="androidx.startup" />
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
193        </provider>
194
195        <receiver
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
196            android:name="androidx.profileinstaller.ProfileInstallReceiver"
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
197            android:directBootAware="false"
197-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
198            android:enabled="true"
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
199            android:exported="true"
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
200            android:permission="android.permission.DUMP" >
200-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
201            <intent-filter>
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
202                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
203            </intent-filter>
204            <intent-filter>
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
205                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
206            </intent-filter>
207            <intent-filter>
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
208                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
208-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
208-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
209            </intent-filter>
210            <intent-filter>
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
211                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
212            </intent-filter>
213        </receiver>
214    </application>
215
216</manifest>
