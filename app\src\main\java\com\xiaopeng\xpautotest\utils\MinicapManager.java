package com.xiaopeng.xpautotest.utils;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * minicap二进制文件管理器
 * 负责minicap和minicap.so文件的部署、权限设置和进程管理
 */
public class MinicapManager {
    private static final String TAG = "MinicapManager";
    
    // minicap文件路径
    private static final String MINICAP_BIN = "minicap";
    private static final String MINICAP_SO = "minicap.so";
    
    private Context context;
    private String minicapPath;
    private String minicapSoPath;
    private String deployDir;
    private Process minicapProcess;

    public MinicapManager(Context context) {
        this.context = context;
        // 直接使用/data/local/tmp/，系统应用应该有权限
        this.deployDir = "/data/local/tmp/";
        this.minicapPath = deployDir + MINICAP_BIN;
        this.minicapSoPath = deployDir + MINICAP_SO;
    }
    
    /**
     * 部署minicap文件到设备
     * @return 是否部署成功
     */
    public boolean deployMinicap() {
        try {
            // 获取设备ABI
            String abi = getDeviceAbi();
            Log.d(TAG, "Device ABI: " + abi);

            boolean minicapDeployed = false;
            boolean minicapSoDeployed = false;

            // 部署minicap二进制文件
            try {
                minicapDeployed = deployAssetFile("minicap/" + abi + "/" + MINICAP_BIN, minicapPath);
                if (!minicapDeployed) {
                    Log.w(TAG, "Failed to deploy minicap binary, will try alternatives");
                }
            } catch (Exception e) {
                Log.w(TAG, "Exception deploying minicap binary", e);
            }

            // 部署minicap.so文件
            try {
                minicapSoDeployed = deployAssetFile("minicap/" + abi + "/" + MINICAP_SO, minicapSoPath);
                if (!minicapSoDeployed) {
                    Log.w(TAG, "Failed to deploy minicap.so, will try alternatives");
                }
            } catch (Exception e) {
                Log.w(TAG, "Exception deploying minicap.so", e);
            }

            // 设置可执行权限
            if (minicapDeployed) {
                try {
                    setExecutablePermission(minicapPath);
                } catch (Exception e) {
                    Log.w(TAG, "Failed to set executable permission", e);
                }
            }

            boolean success = minicapDeployed && minicapSoDeployed;
            if (success) {
                Log.i(TAG, "Minicap deployed successfully");
            } else {
                Log.w(TAG, "Minicap deployment incomplete, will try system alternatives");
            }

            return success;

        } catch (Exception e) {
            Log.e(TAG, "Error deploying minicap", e);
            return false;
        }
    }
    
    /**
     * 启动minicap进程
     * @param width 屏幕宽度
     * @param height 屏幕高度
     * @param socketName 本地套接字名称
     * @return 是否启动成功
     */
    public boolean startMinicap(int width, int height, String socketName) {
        try {
            if (minicapProcess != null && minicapProcess.isAlive()) {
                Log.w(TAG, "Minicap process is already running");
                return true;
            }

            // 尝试启动minicap
            return startMinicapFromSystem(width, height) || startMinicapDirect(width, height);

        } catch (Exception e) {
            Log.e(TAG, "Error starting minicap process", e);
            return false;
        }
    }



    /**
     * 直接启动minicap
     */
    private boolean startMinicapDirect(int width, int height) {
        try {
            // 构建minicap命令
            String command = String.format(
                "cd %s && LD_LIBRARY_PATH=. ./%s -P %dx%d@%dx%d/0 -S",
                deployDir, MINICAP_BIN, width, height, width, height
            );

            Log.d(TAG, "Starting minicap directly: " + command);

            minicapProcess = Runtime.getRuntime().exec(new String[]{"sh", "-c", command});

            // 等待一小段时间确保进程启动
            Thread.sleep(2000);

            if (minicapProcess.isAlive()) {
                Log.i(TAG, "Minicap started successfully directly");
                return true;
            } else {
                Log.e(TAG, "Minicap process failed to start directly");
                return false;
            }

        } catch (Exception e) {
            Log.w(TAG, "Failed to start minicap directly", e);
            return false;
        }
    }

    /**
     * 尝试使用系统中已有的minicap
     */
    private boolean startMinicapFromSystem(int width, int height) {
        try {
            // 检查系统中是否已有minicap
            String[] possiblePaths = {
                "/system/bin/minicap",
                "/system/xbin/minicap",
                "/data/local/tmp/minicap",
                "/vendor/bin/minicap"
            };

            for (String path : possiblePaths) {
                if (new File(path).exists()) {
                    Log.d(TAG, "Found existing minicap at: " + path);

                    String command = String.format(
                        "%s -P %dx%d@%dx%d/0 -S",
                        path, width, height, width, height
                    );

                    Log.d(TAG, "Starting system minicap: " + command);
                    minicapProcess = Runtime.getRuntime().exec(command);

                    Thread.sleep(2000);

                    if (minicapProcess.isAlive()) {
                        Log.i(TAG, "System minicap started successfully");
                        return true;
                    }
                }
            }

            Log.w(TAG, "No system minicap found");
            return false;

        } catch (Exception e) {
            Log.w(TAG, "Failed to start system minicap", e);
            return false;
        }
    }
    
    /**
     * 停止minicap进程
     */
    public void stopMinicap() {
        if (minicapProcess != null) {
            try {
                minicapProcess.destroy();
                minicapProcess.waitFor();
                Log.i(TAG, "Minicap process stopped");
            } catch (InterruptedException e) {
                Log.e(TAG, "Error stopping minicap process", e);
                minicapProcess.destroyForcibly();
            } finally {
                minicapProcess = null;
            }
        }
    }
    
    /**
     * 检查minicap是否正在运行
     */
    public boolean isMinicapRunning() {
        return minicapProcess != null && minicapProcess.isAlive();
    }
    
    /**
     * 获取设备ABI
     */
    private String getDeviceAbi() {
        String abi = Build.SUPPORTED_ABIS[0];
        // 映射到minicap支持的ABI
        switch (abi) {
            case "arm64-v8a":
                return "arm64-v8a";
            case "armeabi-v7a":
                return "armeabi-v7a";
            case "x86":
                return "x86";
            case "x86_64":
                return "x86_64";
            default:
                Log.w(TAG, "Unsupported ABI: " + abi + ", fallback to arm64-v8a");
                return "arm64-v8a";
        }
    }
    
    /**
     * 从assets部署文件到设备
     */
    private boolean deployAssetFile(String assetPath, String targetPath) {
        try {
            // 检查assets文件是否存在
            if (!assetFileExists(assetPath)) {
                Log.w(TAG, "Asset file does not exist: " + assetPath);
                return false;
            }

            // 先复制到应用缓存目录
            File tempFile = new File(context.getCacheDir(), new File(assetPath).getName());

            try (InputStream inputStream = context.getAssets().open(assetPath);
                 OutputStream outputStream = new FileOutputStream(tempFile)) {

                byte[] buffer = new byte[8192];
                int length;
                while ((length = inputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, length);
                }
                outputStream.flush();
            }

            Log.d(TAG, "Temp file created: " + tempFile.getAbsolutePath() + ", size: " + tempFile.length());

            // 使用系统命令复制到目标位置
            String command = String.format("cp %s %s && chmod 755 %s",
                tempFile.getAbsolutePath(), targetPath, targetPath);

            Log.d(TAG, "Executing command: " + command);
            Process process = Runtime.getRuntime().exec(new String[]{"sh", "-c", command});

            // 读取错误输出
            java.io.BufferedReader errorReader = new java.io.BufferedReader(
                new java.io.InputStreamReader(process.getErrorStream()));
            String errorLine;
            StringBuilder errorOutput = new StringBuilder();
            while ((errorLine = errorReader.readLine()) != null) {
                errorOutput.append(errorLine).append("\n");
            }

            int exitCode = process.waitFor();

            // 清理临时文件
            tempFile.delete();

            if (exitCode == 0) {
                Log.d(TAG, "Deployed " + assetPath + " to " + targetPath);
                return true;
            } else {
                Log.e(TAG, "Deploy command failed with exit code: " + exitCode);
                if (errorOutput.length() > 0) {
                    Log.e(TAG, "Error output: " + errorOutput.toString());
                }

                // 尝试直接复制到应用私有目录
                return deployToPrivateDir(assetPath, targetPath);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error deploying asset file: " + assetPath, e);
            return deployToPrivateDir(assetPath, targetPath);
        }
    }

    /**
     * 部署到应用私有目录作为备选方案
     */
    private boolean deployToPrivateDir(String assetPath, String targetPath) {
        try {
            Log.d(TAG, "Trying to deploy to private directory as fallback");

            // 使用应用私有目录
            File privateDir = new File(context.getFilesDir(), "minicap");
            if (!privateDir.exists()) {
                privateDir.mkdirs();
            }

            String privatePath = new File(privateDir, new File(targetPath).getName()).getAbsolutePath();

            try (InputStream inputStream = context.getAssets().open(assetPath);
                 OutputStream outputStream = new FileOutputStream(privatePath)) {

                byte[] buffer = new byte[8192];
                int length;
                while ((length = inputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, length);
                }
                outputStream.flush();
            }

            // 设置可执行权限
            File file = new File(privatePath);
            file.setExecutable(true);

            // 更新路径
            if (targetPath.endsWith("minicap")) {
                minicapPath = privatePath;
                deployDir = privateDir.getAbsolutePath() + "/";
            } else if (targetPath.endsWith("minicap.so")) {
                minicapSoPath = privatePath;
            }

            Log.d(TAG, "Deployed to private directory: " + privatePath);
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error deploying to private directory", e);
            return false;
        }
    }
    
    /**
     * 设置文件可执行权限
     */
    private boolean setExecutablePermission(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                boolean result = file.setExecutable(true, false);
                Log.d(TAG, "Set executable permission for " + filePath + ": " + result);
                return result;
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error setting executable permission", e);
            return false;
        }
    }
    
    /**
     * 检查assets文件是否存在
     */
    private boolean assetFileExists(String assetPath) {
        try {
            InputStream inputStream = context.getAssets().open(assetPath);
            inputStream.close();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 清理部署的文件
     */
    public void cleanup() {
        stopMinicap();

        try {
            File minicapFile = new File(minicapPath);
            File minicapSoFile = new File(minicapSoPath);

            if (minicapFile.exists()) {
                minicapFile.delete();
                Log.d(TAG, "Cleaned up minicap binary");
            }

            if (minicapSoFile.exists()) {
                minicapSoFile.delete();
                Log.d(TAG, "Cleaned up minicap.so");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup", e);
        }
    }
}
