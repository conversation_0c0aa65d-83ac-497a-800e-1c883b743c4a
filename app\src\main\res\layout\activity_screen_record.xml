<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:gravity="center">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="屏幕录制控制"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="32dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="24dp">

        <Button
            android:id="@+id/btn_start_record"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="开始录制"
            android:layout_marginEnd="8dp"
            android:background="@android:color/holo_green_light"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/btn_stop_record"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="停止录制"
            android:layout_marginStart="8dp"
            android:background="@android:color/holo_red_light"
            android:textColor="@android:color/white"
            android:enabled="false" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="状态：未开始"
        android:textSize="16sp"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tv_performance"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="性能监控数据将在此显示"
        android:textSize="14sp"
        android:background="@android:color/black"
        android:textColor="@android:color/white"
        android:padding="12dp"
        android:fontFamily="monospace"
        android:layout_marginBottom="16dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <TextView
            android:id="@+id/tv_logs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="日志信息：\n"
            android:textSize="12sp"
            android:background="@android:color/darker_gray"
            android:textColor="@android:color/white"
            android:padding="8dp"
            android:fontFamily="monospace" />

    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/btn_clear_logs"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="清除日志"
            android:layout_marginEnd="16dp" />

        <Button
            android:id="@+id/btn_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="返回主界面" />

    </LinearLayout>

</LinearLayout>
