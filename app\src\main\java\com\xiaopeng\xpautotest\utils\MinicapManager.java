package com.xiaopeng.xpautotest.utils;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * minicap二进制文件管理器
 * 负责minicap和minicap.so文件的部署、权限设置和进程管理
 */
public class MinicapManager {
    private static final String TAG = "MinicapManager";
    
    // minicap文件路径
    private static final String MINICAP_BIN = "minicap";
    private static final String MINICAP_SO = "minicap.so";
    private static final String DEPLOY_DIR = "/data/local/tmp/";
    
    private Context context;
    private String minicapPath;
    private String minicapSoPath;
    private Process minicapProcess;
    
    public MinicapManager(Context context) {
        this.context = context;
        this.minicapPath = DEPLOY_DIR + MINICAP_BIN;
        this.minicapSoPath = DEPLOY_DIR + MINICAP_SO;
    }
    
    /**
     * 部署minicap文件到设备
     * @return 是否部署成功
     */
    public boolean deployMinicap() {
        try {
            // 获取设备ABI
            String abi = getDeviceAbi();
            Log.d(TAG, "Device ABI: " + abi);
            
            // 部署minicap二进制文件
            if (!deployAssetFile("minicap/" + abi + "/" + MINICAP_BIN, minicapPath)) {
                Log.e(TAG, "Failed to deploy minicap binary");
                return false;
            }
            
            // 部署minicap.so文件
            if (!deployAssetFile("minicap/" + abi + "/" + MINICAP_SO, minicapSoPath)) {
                Log.e(TAG, "Failed to deploy minicap.so");
                return false;
            }
            
            // 设置可执行权限
            if (!setExecutablePermission(minicapPath)) {
                Log.e(TAG, "Failed to set executable permission for minicap");
                return false;
            }
            
            Log.i(TAG, "Minicap deployed successfully");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error deploying minicap", e);
            return false;
        }
    }
    
    /**
     * 启动minicap进程
     * @param width 屏幕宽度
     * @param height 屏幕高度
     * @param socketName 本地套接字名称
     * @return 是否启动成功
     */
    public boolean startMinicap(int width, int height, String socketName) {
        try {
            if (minicapProcess != null && minicapProcess.isAlive()) {
                Log.w(TAG, "Minicap process is already running");
                return true;
            }
            
            // 构建minicap命令
            String command = String.format(
                "LD_LIBRARY_PATH=%s %s -P %dx%d@%dx%d/0 -S",
                DEPLOY_DIR, minicapPath, width, height, width, height
            );
            
            Log.d(TAG, "Starting minicap with command: " + command);
            
            ProcessBuilder processBuilder = new ProcessBuilder("sh", "-c", command);
            processBuilder.redirectErrorStream(true);
            
            minicapProcess = processBuilder.start();
            
            // 等待一小段时间确保进程启动
            Thread.sleep(1000);
            
            if (minicapProcess.isAlive()) {
                Log.i(TAG, "Minicap process started successfully");
                return true;
            } else {
                Log.e(TAG, "Minicap process failed to start");
                return false;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting minicap process", e);
            return false;
        }
    }
    
    /**
     * 停止minicap进程
     */
    public void stopMinicap() {
        if (minicapProcess != null) {
            try {
                minicapProcess.destroy();
                minicapProcess.waitFor();
                Log.i(TAG, "Minicap process stopped");
            } catch (InterruptedException e) {
                Log.e(TAG, "Error stopping minicap process", e);
                minicapProcess.destroyForcibly();
            } finally {
                minicapProcess = null;
            }
        }
    }
    
    /**
     * 检查minicap是否正在运行
     */
    public boolean isMinicapRunning() {
        return minicapProcess != null && minicapProcess.isAlive();
    }
    
    /**
     * 获取设备ABI
     */
    private String getDeviceAbi() {
        String abi = Build.SUPPORTED_ABIS[0];
        // 映射到minicap支持的ABI
        switch (abi) {
            case "arm64-v8a":
                return "arm64-v8a";
            case "armeabi-v7a":
                return "armeabi-v7a";
            case "x86":
                return "x86";
            case "x86_64":
                return "x86_64";
            default:
                Log.w(TAG, "Unsupported ABI: " + abi + ", fallback to arm64-v8a");
                return "arm64-v8a";
        }
    }
    
    /**
     * 从assets部署文件到设备
     */
    private boolean deployAssetFile(String assetPath, String targetPath) {
        try (InputStream inputStream = context.getAssets().open(assetPath);
             OutputStream outputStream = new FileOutputStream(targetPath)) {
            
            byte[] buffer = new byte[8192];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
            outputStream.flush();
            
            Log.d(TAG, "Deployed " + assetPath + " to " + targetPath);
            return true;
            
        } catch (IOException e) {
            Log.e(TAG, "Error deploying asset file: " + assetPath, e);
            return false;
        }
    }
    
    /**
     * 设置文件可执行权限
     */
    private boolean setExecutablePermission(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                boolean result = file.setExecutable(true, false);
                Log.d(TAG, "Set executable permission for " + filePath + ": " + result);
                return result;
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error setting executable permission", e);
            return false;
        }
    }
    
    /**
     * 清理部署的文件
     */
    public void cleanup() {
        stopMinicap();
        
        try {
            File minicapFile = new File(minicapPath);
            File minicapSoFile = new File(minicapSoPath);
            
            if (minicapFile.exists()) {
                minicapFile.delete();
                Log.d(TAG, "Cleaned up minicap binary");
            }
            
            if (minicapSoFile.exists()) {
                minicapSoFile.delete();
                Log.d(TAG, "Cleaned up minicap.so");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup", e);
        }
    }
}
